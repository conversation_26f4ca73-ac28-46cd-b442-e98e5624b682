const mongoose = require('mongoose');

const purchaseReceiptSchema = new mongoose.Schema({
  createdAt: { type: Date, default: () => new Date() },
  user: { type: String, ref: 'User' },
  promoCode: { type: String },
  saleReason: { type: String },

  service: { type: String }, // 'apple' or 'google'
  productId: { type: String },
  purchaseDate: { type: Date, default: () => new Date() },
  expirationDate: { type: Date },
  revokedAt: { type: Date },
  cancelledAt: { type: Date },
  isRefund: { type: Boolean },
  isFraudulent: { type: Boolean },

  transactionId: { type: String, unique: true },
  subscriptionId: { type: String },
  originalPurchaseDate: { type: Date },

  fullReceipt: Object,

  daysOnPlatformBeforePurchase: { type: Number },
  numberOfMatchesBeforePurchase: { type: Number },
  numberOfLikesSentBeforePurchase: { type: Number },
  userMetrics: { type: mongoose.Mixed },
  userEvents: { type: mongoose.Mixed },

  renewalNumber: { type: Number },
  country: { type: String },
  timezone: { type: String },
  revenue: { type: Number },
  currency: { type: String },
  price: { type: Number },
  kochava: { type: mongoose.Mixed },
  kochavaNetwork: { type: String },
  appsflyer: { type: mongoose.Mixed },
  utm_source: { type: String },
  utm_medium: { type: String },
  utm_campaign: { type: String },
  utm_content: { type: String },
  adset_name: { type: String },

  purchasedFrom: { type: String },
});

purchaseReceiptSchema.index({
  subscriptionId: 1,
});

purchaseReceiptSchema.index({
  purchaseDate: 1,
});

purchaseReceiptSchema.index({
  user: 1,
});

// Export schema =====================================================================================================================================================================
module.exports = mongoose.model('PurchaseReceipt', purchaseReceiptSchema);
