const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: () => new Date() },
  questionId: { type: String, required: true },
  deviceId: { type: String, required: true },
});

schema.index({ questionId: 1, deviceId: 1 }, { unique: true });

// Export schema =====================================================================================================================================================================
let conn = connectionLib.getEventsConnection() || mongoose;
module.exports = conn.model('QuestionWebView', schema);
