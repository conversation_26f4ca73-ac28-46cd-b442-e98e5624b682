const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema(
  {
    createdAt: { type: Date, default: Date.now, index: true },
    bucket: { type: String },
    prefix: { type: String },
    key: { type: String },                                       // the S3 object key
    code: { type: String },                                      // AWS error code, e.g. "AccessDenied"
    message: { type: String },                                   // AWS error message
    retryCount: { type: Number, default: 1 },                    // we retried once
  },
);

const connection = connectionLib.getEventsConnection() || mongoose;
module.exports = connection.model('S3DeleteFailure', schema);
