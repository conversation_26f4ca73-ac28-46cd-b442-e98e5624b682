const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now },
  admin: { type: String, ref: 'User' },
  log: { type: String },
});

// Export schema =====================================================================================================================================================================
let conn = connectionLib.getEventsConnection() || mongoose;
module.exports = mongoose.model('AdminActionLog', schema);
