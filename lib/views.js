const moment = require('moment');
const httpErrors = require('../lib/http-errors');
const User = require('../models/user');
const ProfileView = require('../models/profile-view');
const premiumLib = require('../lib/premium');
const { getBlockLookup, profilePreviewProjection } = require('../lib/projections');
const profilesLib = require('../lib/profiles-v3');
const constants = require('../lib/constants');

async function getProfileViews(user, beforeDate, direction, includeSource) {
  const lookupField = direction == 'to' ? 'from' : 'to';
  const matchBy = {
    [direction]: user._id,
    createdAt: { $gt: moment().subtract(7, 'days').toDate() },
  };
  if (beforeDate) {
    if (!premiumLib.isPremiumV1OrGodMode(user)) {
      throw httpErrors.forbiddenError();
    }
    matchBy.createdAt.$lt = new Date(beforeDate);
  }

  const userLookupMatch = {
    shadowBanned: { $ne: true },
    admin: { $ne: true },
    'block.0': { $exists: false },
    _id: { $ne: user._id },
  };
  if (direction == 'to') {
    userLookupMatch.hideProfileViews = { $ne: true };

    if (constants.hideUnverifiedUsers() && user.hideUnverifiedUsers !== false) {
      userLookupMatch['verification.status'] = { $in: ['verified', 'reverifying'] };
    }
  }

  const limit = constants.getPageSize();
  let rv = [];

  // Use different projections based on version
  const needsEnhancedData = user.versionAtLeast('1.13.104');
  let projection = { ...profilePreviewProjection };

  if (needsEnhancedData) {
    projection = {
      ...projection,
      interestNames: 1,
      createdAt: 1,
      'metrics.lastSeen': 1,
      'metrics.numActionsReceived': 1,
      'scores.likeRatio': 1,
      coordinates: 1,
      location: 1,
    };
  }

  while (rv.length < limit) {
    const views = await ProfileView.find(matchBy).sort('-createdAt').limit(limit).lean();
    if (views.length == 0) {
      break;
    }

    const userIds = views.map(x => x[lookupField]);
    const users = await User.aggregate([
      {
        $match: {
          _id: { $in: userIds },
        },
      },
      getBlockLookup('$_id', user._id),
      {
        $match: userLookupMatch
      },
      {
        $project: projection,
      },
    ]);

    for (const view of views) {
      const foundUser = users.find(x => x._id == view[lookupField]);
      if (foundUser) {
        if (needsEnhancedData) {
          foundUser.tags = profilesLib.getProfileTags(user, foundUser);
          delete foundUser.createdAt;
          delete foundUser['metrics.lastSeen'];
          delete foundUser['metrics.numActionsReceived'];
          delete foundUser['scores.likeRatio'];
          delete foundUser.coordinates;
          delete foundUser.location;
        }

        rv.push({
          createdAt: view.createdAt,
          source: includeSource ? view.source : undefined,
          user: foundUser,
        });
      }
    }

    // set filter in case we need to repeat
    matchBy.createdAt.$lt = views[views.length - 1].createdAt;
  }

  return rv;
}

module.exports = {
  getProfileViews,
}
