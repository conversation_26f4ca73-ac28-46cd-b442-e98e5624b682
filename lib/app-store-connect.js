const fs = require('fs');
const jwt = require('jsonwebtoken');
const axios = require('axios');
const path = require('path');
const { AppStoreServerAPI, Environment, decodeRenewalInfo, decodeTransaction, decodeTransactions } = require('app-store-server-api');

let APP_STORE_PRIVATE_KEY = null;
const KEY_PATH = 'app-store-connect-private-key.pem';
const KEY_ID = '35FVW645A6';
const ISSUER_ID = '0510c0c4-cf9f-4104-83f5-f577ec598e78';
const APP_BUNDLE_ID = 'enterprises.dating.boo';
const NODE_ENV = process.env.NODE_ENV || 'test';

const apiInstances = {
  prod: null,
  beta: null,
  test: null,
};

// Initializes both Production and Sandbox API instances
function initializeAppStoreAPIInstances() {
  console.log(`[storekit2] Initializing private key: ${KEY_PATH}`);
  if (!fs.existsSync(KEY_PATH)) {
    console.log(`[storekit2] Private key file not found: ${KEY_PATH}`);
    return;
  }

  try {
    APP_STORE_PRIVATE_KEY = fs.readFileSync(KEY_PATH, 'utf8');
    console.log('[storekit2] Successfully initialized private key');

    apiInstances.prod = new AppStoreServerAPI(
      APP_STORE_PRIVATE_KEY,
      KEY_ID,
      ISSUER_ID,
      APP_BUNDLE_ID,
      Environment.Production,
    );

    apiInstances.beta = new AppStoreServerAPI(
      APP_STORE_PRIVATE_KEY,
      KEY_ID,
      ISSUER_ID,
      APP_BUNDLE_ID,
      Environment.Sandbox,
    );

    console.log('[storekit2] Successfully initialized both Production and Sandbox App Store API instances');
  } catch (err) {
    console.log('[storekit2] Error initializing App Store Server API:', err);
  }
}

// Call initialization once at startup
if (!apiInstances.prod || !apiInstances.beta) {
  initializeAppStoreAPIInstances();
}

// Optional override for tests or mocking
function initializeAppStoreAPI(customApi) {
  if (customApi) {
    apiInstances.test = customApi;
    console.log('[storekit2] App Store API initialized with custom API instance');
  }
}

function getApi(overrideEnv) {
  const env = overrideEnv || NODE_ENV;

  if (!apiInstances[env]) {
    console.log(`[storekit2] getApi(): No API instance found for environment: ${env}`);
    return null;
  }

  return apiInstances[env];
}

function getToken() {
  if (!APP_STORE_PRIVATE_KEY) {
    console.error('Private key not loaded.');
    return null;
  }
  let now = Math.round(new Date().getTime() / 1000);
  let nowPlus20 = now + 1199;

  let payload = {
    iss: ISSUER_ID,
    exp: nowPlus20,
    aud: 'appstoreconnect-v1',
  };

  let signOptions = {
    algorithm: 'ES256',
    header: {
      alg: 'ES256',
      kid: KEY_ID,
      typ: 'JWT',
    },
  };

  let token = jwt.sign(payload, APP_STORE_PRIVATE_KEY, signOptions);
  return token;
}

async function getReplyToReview(reviewId) {
  let token = getToken();
  if (!token) return;
  const config = {
    method: 'get',
    url: `https://api.appstoreconnect.apple.com/v1/customerReviews/${reviewId}/response`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };

  try {
    let r = await axios(config);
    // console.log('success', JSON.stringify(r.data, null, 2));
    return r.data.data;
  } catch (error) {
    console.log(error);
  }
}

async function getReviews(url) {
  if (!url) {
    url = 'https://api.appstoreconnect.apple.com/v1/apps/1498407272/customerReviews?exists%5BpublishedResponse%5D=false&sort=-createdDate';
  }
  let token = getToken();
  if (!token) return;
  const config = {
    method: 'get',
    url,
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };

  try {
    let r = await axios(config);
    // console.log('success', JSON.stringify(r.data, null, 2));
    return r.data;
  } catch (error) {
    console.log(error);
  }
}

async function replyToReview(reviewId, replyText) {
  let token = getToken();
  if (!token) return;
  const config = {
    method: 'post',
    url: 'https://api.appstoreconnect.apple.com/v1/customerReviewResponses',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: {
      data: {
        attributes: {
          responseBody: replyText,
        },
        relationships: {
          review: {
            data: {
              id: reviewId,
              type: 'customerReviews',
            },
          },
        },
        type: 'customerReviewResponses',
      },
    },
  };

  try {
    let r = await axios(config);
    // console.log('success', JSON.stringify(r.data, null, 2));
  } catch (error) {
    console.log(error);
  }
}

async function getTransaction(originalTransactionId, transactionId) {
  try {
    let options;
    while (true) {
      const response = await getApi().getTransactionHistory(originalTransactionId, options);
      const transactions = await decodeTransactions(response.signedTransactions);
      const found = transactions.find((x) => x.transactionId == transactionId);
      if (found) {
        if (found.price) {
          found.price = found.price / 1000;
        }
        return found;
      }
      if (!response.hasMore) {
        return;
      }
      // get next page
      options = { revision: response.revision };
    }
  } catch (err) {
    console.log(err);
  }
}

async function getTransactionHistory(api, originalTransactionId) {
  if (!originalTransactionId) {
    return [];
  }

  let options;
  const allTransactions = [];
  let hasMore = true;

  while (hasMore) {
    const response = await api.getTransactionHistory(originalTransactionId, options);
    console.log('[storekit2]: getTransactionHistory: response', JSON.stringify(response));
    const txs = await decodeTransactions(response.signedTransactions);
    allTransactions.push(...txs);

    hasMore = response.hasMore;
    if (hasMore) {
      options = { revision: response.revision };
    }
  }

  return allTransactions;
}

async function validate(jws, cb) {
  try {
    const transactionInfo = await decodeTransaction(jws);
    if (!transactionInfo?.originalTransactionId) {
      // Treat as hacked/tampered: return status: 2 in validatedData
      return cb(`[storekit2]: validate: jws: Missing originalTransactionId in JWS payload for jws: ${jws}`, { status: 2 });
    }

    let apiInstance;
    if (NODE_ENV === 'test' && getApi('test')) {
      apiInstance = getApi('test');
    } else {
      apiInstance = transactionInfo.environment === 'Production' ? getApi('prod') : getApi('beta');
      if (NODE_ENV === 'prod' && transactionInfo.environment !== 'Production') {
        console.log(`[storekit2]: validate: jws: Warning: Mismatched environment. NODE_ENV is 'prod' but transaction environment is '${transactionInfo.environment}' for jws: ${jws}`);
      }
    }

    const history = await getTransactionHistory(apiInstance, transactionInfo.originalTransactionId);
    if (history.length === 0) {
      // Treat as hacked/tampered: return status: 2 in validatedData
      return cb(`[storekit2]: validate: jws: No transaction history found for trxId: ${transactionInfo.originalTransactionId} for jws: ${jws}`, { status: 2 });
    }

    cb(null, history);
  } catch (err) {
    // decodeTransaction errors (invalid signature, bad cert chain, tampered JWS)
    if (/certificate validation/i.test(err.message) || /signature/i.test(err.message)) {
      return cb(`[storekit2]: validate: jws: Tampered or fake JWS: ${err.message} for jws: ${jws}`, { status: 2 });
    }

    // Fallback: unexpected system error
    cb(err);
  }
}

function getPurchaseData(validatedData, options = {}) {
  if (!validatedData) return [];

  const transactions = Array.isArray(validatedData) ? validatedData : [validatedData];

  const toDateVariants = (ms) => {
    if (!ms) return {};
    const d = new Date(Number(ms));
    return {
      date: d.toISOString().replace('T', ' ').replace(/\.\d+Z$/, ' Etc/GMT'),
      ms: Number(ms),
      pst: d.toLocaleString('en-US', { timeZone: 'America/Los_Angeles' }),
    };
  };

  return transactions
    .map((t) => {
      const canceled = !!t.revocationDate;
      if (canceled && options.ignoreCanceled) return null;

      const purchase = toDateVariants(t.purchaseDate);
      const originalPurchase = toDateVariants(t.originalPurchaseDate);
      const expires = toDateVariants(t.expiresDate);
      const cancellation = toDateVariants(t.revocationDate);

      const receipt = {
        quantity: t.quantity || 1,
        productId: t.productId,
        transactionId: t.transactionId,
        originalTransactionId: t.originalTransactionId,
        bundleId: t.bundleId,
        inAppOwnershipType: t.inAppOwnershipType || 'PURCHASED',
        isTrialPeriod: String(t.isTrialPeriod || false),
        isInIntroOfferPeriod: String(t.isInIntroOfferPeriod || false),
        isTrial: !!t.isTrialPeriod,
        expirationDate: expires.ms || 0, // consumables → 0
      };

      if (purchase.date) {
        receipt.purchaseDate = purchase.date;
        receipt.purchaseDateMs = purchase.ms;
        receipt.purchaseDatePst = purchase.pst;
      }

      if (originalPurchase.date) {
        receipt.originalPurchaseDate = originalPurchase.date;
        receipt.originalPurchaseDateMs = originalPurchase.ms;
        receipt.originalPurchaseDatePst = originalPurchase.pst;
      }

      if (expires.date) {
        receipt.expiresDate = expires.date;
        receipt.expiresDateMs = expires.ms;
        receipt.expiresDatePst = expires.pst;
      }

      if (cancellation.date) {
        receipt.cancellationDate = cancellation.ms;
      }

      if (t.webOrderLineItemId) {
        receipt.webOrderLineItemId = t.webOrderLineItemId;
      }

      // StoreKit 2 extras if present
      if (t.type) receipt.type = t.type;
      if (t.transactionReason) receipt.transactionReason = t.transactionReason;
      if (t.signedDate) receipt.signedDate = t.signedDate;
      if (t.appTransactionId) receipt.appTransactionId = t.appTransactionId;
      if (t.storefront) receipt.storefront = t.storefront;
      if (t.storefrontId) receipt.storefrontId = t.storefrontId;
      if (t.price) receipt.price = t.price / 1000;
      if (t.currency) receipt.currency = t.currency;
      if (t.deviceVerification) receipt.deviceVerification = t.deviceVerification;
      if (t.deviceVerificationNonce) receipt.deviceVerificationNonce = t.deviceVerificationNonce;

      return receipt;
    })
    .filter(Boolean);
}

module.exports = {
  getReviews,
  getReplyToReview,
  replyToReview,
  getApi,
  getTransaction,
  validate,
  getPurchaseData,
  initializeAppStoreAPI,
};
