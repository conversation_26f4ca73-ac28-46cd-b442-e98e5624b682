const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');

// Use absolute path to ensure it works regardless of where the script is run from
const credentialsPath = path.join(__dirname, '..', 'boo-dating-prod-google-sheet-credentials.json');

let GOOGLE_CREDENTIALS = null
if (fs.existsSync(credentialsPath)) {
    GOOGLE_CREDENTIALS = require(credentialsPath);
}

function createSheetsClient({
    spreadsheetId,
    hasHeader = false,
    defaults = { sheetName: 'Sheet1', statusColumn: 'C', dataColumns: 'A:C' },
    columnMap = {}
}) {
    if (!spreadsheetId || !GOOGLE_CREDENTIALS) throw new Error('spreadsheetId is required or GOOGLE_CREDENTIALS file missing');
    const auth = new google.auth.GoogleAuth({
        credentials: GOOGLE_CREDENTIALS,
        scopes: ['https://www.googleapis.com/auth/spreadsheets']
    });
    const sheets = google.sheets({ version: 'v4', auth });

    return {
        sheets,
        spreadsheetId,
        defaults,
        hasHeader,
        columnMap
    };
}

async function getPendingRows(client, options = {}) {
    const { sheetName, dataColumns, filterValue } = { ...client.defaults, ...options };
    const response = await client.sheets.spreadsheets.values.get({
        spreadsheetId: client.spreadsheetId,
        range: `${sheetName}!${dataColumns}`,
    });

    const rows = response.data.values || [];
    const dataRows = client.hasHeader ? rows.slice(1) : rows;
    const startIndex = client.hasHeader ? 2 : 1;

    return dataRows.map((row, index) => ({
        ...mapRow(row, client.columnMap),
        rowNumber: index + startIndex,
    })).filter(({ status }) => status !== filterValue);
}

async function getRows(client, options = {}) {
    const { sheetName, dataColumns } = { ...client.defaults, ...options };
    const response = await client.sheets.spreadsheets.values.get({
        spreadsheetId: client.spreadsheetId,
        range: `${sheetName}!${dataColumns}`,
    });

    const rows = response.data.values || [];
    const dataRows = client.hasHeader ? rows.slice(1) : rows;
    return dataRows.map(row => mapRow(row, client.columnMap));
}

async function updateCell(client, options) {
    const { sheetName, statusColumn } = { ...client.defaults, ...options };
    await client.sheets.spreadsheets.values.update({
        spreadsheetId: client.spreadsheetId,
        range: `${sheetName}!${statusColumn}${options.rowNumber}`,
        valueInputOption: 'USER_ENTERED',
        resource: { values: [[options.value]] },
    });
}

function mapRow(row, columnMap) {
    const mapped = { raw: row };
    for (const [key, index] of Object.entries(columnMap)) {
        mapped[key] = row[index] || '';
    }
    return mapped;
}

module.exports = {
    createSheetsClient,
    getPendingRows,
    getRows,
    updateCell,
};
