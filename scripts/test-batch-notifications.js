const { sendBatchNotifications } = require('../config/firebase-admin');
const mongoose = require('mongoose');
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';
// GOOGLE_APPLICATION_CREDENTIALS environment variable should be set

(async () => {
  try {
    // connection to database needed to update the success metrics and failure count
    console.log('Connecting to database...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to database');

    console.log('Testing sendBatchNotifications with mixed valid/invalid tokens...\n');

    const validToken1 = 'your_first_valid_fcm_token_here';  // Replace with your first FCM token
    const validToken2 = 'your_second_valid_fcm_token_here'; // Replace with your second FCM token

    const mockUsers = [
      {
        _id: 'user1',
        firstName: 'user1',
        fcmToken: validToken1,
        pushNotificationSettings: { test: true },
        locale: 'en',
        appVersion: '1.13.71',
      },
      {
        _id: 'user2',
        firstName: 'user2',
        fcmToken: validToken2,
        pushNotificationSettings: { test: true },
        locale: 'en',
        appVersion: '1.13.71',
      },
      {
        _id: 'user3',
        firstName: 'user3',
        fcmToken: 'invalid_token_123', // Invalid token
        pushNotificationSettings: { test: true },
        locale: 'en',
        appVersion: '1.13.71',
      }
    ];

    const messages = mockUsers.map(user => ({
      token: user.fcmToken,
      notification: {
        title: `Test Notification for bulk notifications: ${user._id}`,
        body: `This is a test notification for bulk notifications: ${user._id}`,
      },
    }));

    const userIdToken = new Map()

    mockUsers.forEach(user => {
      userIdToken.set(user.fcmToken, user._id);
    });


    await sendBatchNotifications(messages, userIdToken);

  } catch (error) {
    console.error('Error during batch notifications test:', error);
  }
})();
