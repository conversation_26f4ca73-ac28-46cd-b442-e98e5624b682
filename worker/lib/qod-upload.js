const { createQod } = require('../../lib/social');
const googleSheetsManager = require('../../lib/google-sheets-manager');

let QOD_SHEET_ID = process.env.QOD_SHEET_ID || '1kSMtzNbTxEQXN2djWsUNgKxLrllLBHa-omN0rAlTNw8';
if (process.env.NODE_ENV == 'beta') {
  QOD_SHEET_ID = '17poOVPOPlRNRLBi7DE1kL4JX6N3dpV1MxzpNRmRFxkg';
}
if (process.env.NODE_ENV == 'prod') {
  QOD_SHEET_ID = '1kSMtzNbTxEQXN2djWsUNgKxLrllLBHa-omN0rAlTNw8';
}

const log = (msg) => console.log(`[executeQodUploadSheet]: ${msg}`);

function createQodSheetsClient() {
  return googleSheetsManager.createSheetsClient({
    spreadsheetId: QOD_SHEET_ID,
    hasHeader: true,
    defaults: { sheetName: 'Sheet1', statusColumn: 'C', dataColumns: 'A:C' },
    columnMap: { language: 0, text: 1, status: 2 }
  });
}

function validateQodData(qod) {
  const errors = [];
  const normalizedQod = {};

  const text = qod.text || qod.Text || qod.TEXT;
  if (!text || text.trim() === '') {
    errors.push('Missing required field: text');
  } else {
    normalizedQod.text = text.trim();
  }

  const language = qod.language || qod.Language || qod.LANGUAGE || 'en';
  normalizedQod.language = language.trim();

  return {
    isValid: errors.length === 0,
    errors,
    normalizedQod: errors.length === 0 ? normalizedQod : null,
  };
}

async function markQodAsUploaded(client, rowIndex, uploadedColumn = 'C') {
  await googleSheetsManager.updateCell(client, {
    rowNumber: rowIndex,
    value: 'UPLOAD-COMPLETED',
    statusColumn: uploadedColumn,
  });
}

async function executeQodUploadSheet(req, res) {
  try {
    if (!process.env.TESTING) {
      res.json({});
    }

    const qodSheetClient = createQodSheetsClient();
    const pendingRows = await googleSheetsManager.getPendingRows(qodSheetClient, {
      filterValue: 'UPLOAD-COMPLETED',
    });

    log(`Found ${pendingRows.length} pending QODs`);

    if (pendingRows.length > 0) await processQodData(qodSheetClient, pendingRows);

    if (process.env.TESTING) {
      res.json({});
    }

  } catch (err) {
    log('QOD upload failed:', err);
  }
}

async function processQodData(qodSheetClient, pendingRows) {
  try {
    // 1 seconds between requests to stay under 60 per minute as there free tier limit to 60 writes per minute
    const delay = 1000;

    for (const qodData of pendingRows) {
      try {
        const validation = validateQodData(qodData);

        if (!validation.isValid) continue;

        const question = await createQod({
          text: validation.normalizedQod.text,
          createdBy: null,
          language: validation.normalizedQod.language,
        });

        await markQodAsUploaded(qodSheetClient, qodData.rowNumber);

        log(`Finished creating QOD ${qodData.rowNumber}: ${question.text}`);

        await new Promise(resolve => setTimeout(resolve, delay));
      } catch (error) {
        log(`Failed to process row ${qodData.rowNumber}:`, error);
      }
    }

    log(`Completed processing ${pendingRows.length} QODs`);
  } catch (error) {
    log('Error in processQodData:', error);
  }
}

module.exports = {
  executeQodUploadSheet,
  processQodData,
  validateQodData,
  markQodAsUploaded,
};
