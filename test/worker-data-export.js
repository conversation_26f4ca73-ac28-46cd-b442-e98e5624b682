const { expect } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const { app, validImagePath, initSocket, getSocketPromise, destroySocket } = require('./common');
const { fakeSES } = require('./stub');
const User = require('../models/user');
const Chat = require('../models/chat');
const ChatExportHistory = require('../models/chat-export-history');
const DataRequestHistory = require('../models/data-request-history');
const { translateEmail, getUnsubLink } = require('../lib/email');
const { BOO_BOT_ID } = require('../lib/chat');
const cfsign = require('aws-cloudfront-sign');
const reportLib = require('../lib/report');

describe('test automatic chat export', async () => {
  let sesStub;

  beforeEach(async () => {
    let res = await request(app).get('/v1/user').set('authorization', BOO_BOT_ID);
    expect(res.status).to.equal(200);

    sesStub = sinon.stub(fakeSES, 'sendTemplatedEmail').callsFake((params) => ({
      promise: () =>
        new Promise((resolve, reject) => {
          resolve({});
        }),
    }));

    for (let uid = 0; uid < 2; uid++) {
      res = await request(app).get('/v1/user').set('authorization', uid);
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);
    }

    res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message').set('authorization', 1).send({
      user: '0',
      text: 'msg2',
    });
    expect(res.status).to.equal(200);
  });

  it('prepare chat export url', async () => {
    const user0 = await User.findOne({ _id: '0' });
    user0.locale = 'bn';
    user0.appVersion = '1.13.64';
    await user0.save();
    const socket0 = await initSocket(0);
    const socketPromise = getSocketPromise(socket0, 'approved chat');

    const user1 = await User.findOne({ _id: '1' });
    user1.locale = 'es';
    await user1.save();
    const socket1 = await initSocket(1);
    const socketPromise1 = getSocketPromise(socket1, 'approved chat');

    let res = await request(app).get('/v1/chat').set('authorization', 1);
    expect(res.status).to.equal(200);
    const chatId = res.body[0]._id;

    res = await request(app).post('/v1/chat/exportChat').set('authorization', 1).send({ chatId });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/chat/exportChat').set('authorization', 0).send({ chatId });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/worker-data-export/exportChats').set('authorization', 1);
    expect(res.status).to.equal(200);

    // wait for few milliseconds to wait for export job
    await new Promise((r) => setTimeout(r, 100));

    sinon.assert.calledOnce(sesStub);

    const sesParams = {
      Destination: { ToAddresses: ['<EMAIL>'] },
      Source: 'Boo <<EMAIL>>',
      Template: 'chat-download-v1',
      TemplateData: JSON.stringify({
        SUBJECT: translateEmail({ phrase: 'Your Chat Export is Now Available', locale: user1.locale }),
        FNAME: user1.firstName,
        UNSUBSCRIBE_LINK: getUnsubLink(user1),
        USER_IMAGE: 'MOCK_IMAGE_DOMAIN/undefined',
        GREETINGS: translateEmail({ phrase: 'Hello {{name}}', locale: user1.locale }, { name: user1.firstName }),
        MESSAGE: translateEmail(
          {
            phrase: `We have compiled all the information associated with your conversation with {{other_name}}. You can download it using the link below, valid for 7 days. If you have any questions, please reach out to us.`,
            locale: user1.locale,
          },
          { other_name: user0.firstName },
        ),
        DOWNLOAD_NOW: translateEmail({ phrase: 'Download Data', locale: user1.locale }),
        DOWNLOAD_LINK: 'MOCK_SIGNED_URL',
        THANK_YOU: translateEmail({ phrase: 'Love,', locale: user1.locale }),
        TEAM_BOO: translateEmail({ phrase: 'The Boo Team', locale: user1.locale }),
      }),
      ConfigurationSetName: 'default',
      Tags: [{ Name: 'template', Value: 'chat-download-v1' }],
    };

    sinon.assert.match(sesStub.firstCall.args[0], sesParams);

    let history = await ChatExportHistory.find({ status: 'completed' });
    expect(history.length).to.equal(1);

    res = await socketPromise;
    expect(res.lastMessage.sender).to.equal(BOO_BOT_ID);
    expect(res.lastMessage.text).to.equal(
      `${translateEmail(
        {
          phrase: `We have compiled all the information associated with your conversation with {{other_name}}. You can download it using the link below, valid for 7 days.`,
          locale: user0.locale,
        },
        { other_name: user1.firstName },
      )}\n\nMOCK_SIGNED_URL`,
    );
    expect(res.noreply).to.equal(true);

    const chat = await Chat.find({ users: BOO_BOT_ID });
    expect(chat.length).to.equal(1);
    expect(chat[0].messaged).to.equal(true);

    // Download link will not be passed to user 1 via chat/socket
    try {
      res = await socketPromise1;
    } catch (error) {
      // console.log(error);
      expect(error.message).to.equal('Failed to get reponse, connection timed out...');
    }

    await destroySocket(socket0);
    await destroySocket(socket1);

    res = await request(app).get('/v1/chat/messaged').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal(BOO_BOT_ID);
  });

  it('should add error to db for failed exports', async () => {
    let res = await request(app).get('/v1/chat').set('authorization', 1);
    expect(res.status).to.equal(200);
    const chatId = res.body[0]._id;

    res = await request(app).post('/v1/message/image').set('authorization', 1).query({ chatId }).attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/chat/exportChat').set('authorization', 1).send({ chatId });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/chat/exportChat').set('authorization', 0).send({ chatId });
    expect(res.status).to.equal(200);

    let history = await ChatExportHistory.find({});
    expect(history.length).to.equal(1);
    expect(history[0].error).to.equal(undefined);

    // should add error to db as export will fail due to signed url generation failure
    cfsign.getSignedUrl.restore();
    sinon.stub(cfsign, 'getSignedUrl').throws(new Error('Failed to get signed URL'));
    res = await request(app).post('/v1/worker-data-export/exportChats').set('authorization', 1);
    expect(res.status).to.equal(200);

    history = await ChatExportHistory.find({});
    expect(history.length).to.equal(1);
    expect(history[0].status).to.equal('approved');
    expect(history[0]).to.have.property('error');
    expect(history[0]).to.have.property('errorAt');
    expect(history[0].processingTimeSec).to.not.equal(undefined);
    expect(history[0].error).to.include('Failed to get signed URL');

    // media download error should not cause export failed
    cfsign.getSignedUrl.restore();
    sinon.stub(cfsign, 'getSignedUrl').callsFake((url, options) => `${url}?signature=mocked`);
    res = await request(app).post('/v1/worker-data-export/exportChats').set('authorization', 1);
    expect(res.status).to.equal(200);

    history = await ChatExportHistory.find({});
    expect(history.length).to.equal(1);
    expect(history[0].status).to.equal('completed');
    expect(history[0].processingTimeSec).to.not.equal(undefined);
  });
});

describe('test automatic data export', async () => {
  let sesStub;
  beforeEach(async () => {
    await request(app).get('/v1/user').set('authorization', BOO_BOT_ID);

    sesStub = sinon.stub(fakeSES, 'sendTemplatedEmail').callsFake((params) => ({
      promise: () =>
        new Promise((resolve, reject) => {
          resolve({});
        }),
    }));

    for (let uid = 0; uid < 3; uid++) {
      let res = await request(app).get('/v1/user').set('authorization', uid);
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);
    }
  });

  it('prepare data download url', async () => {
    let res = await request(app).post('/v1/user/requestData').set('authorization', 0);
    expect(res.status).to.equal(200);

    const user1 = await User.findOne({ _id: '1' });
    user1.locale = 'es';
    user1.appVersion = '1.13.64';
    await user1.save();
    const socket1 = await initSocket(1);
    const socketPromise1 = getSocketPromise(socket1, 'approved chat');

    res = await request(app).post('/v1/user/requestData').set('authorization', 1);
    expect(res.status).to.equal(200);

    const user0 = await User.findOne({ _id: '0' });
    user0.locale = 'bn';
    user0.appVersion = '1.13.64';
    await user0.save();
    const socket0 = await initSocket(0);
    const socketPromise = getSocketPromise(socket0, 'approved chat');

    res = await request(app).post('/v1/user/requestData').set('authorization', 2);
    expect(res.status).to.equal(200);

    const user2 = await User.findOne({ _id: '2' });
    user2.locale = 'es';
    user2.appVersion = '1.13.73'; // Markdown in message
    await user2.save();
    const socket2 = await initSocket(2);
    const socketPromise2 = getSocketPromise(socket2, 'approved chat');

    res = await request(app).post('/v1/worker-data-export/exportData').set('authorization', 0);
    expect(res.status).to.equal(200);

    // wait for few milliseconds to wait for export job
    await new Promise((r) => setTimeout(r, 100));

    let history = await DataRequestHistory.find({ status: 'completed' });
    expect(history.length).to.equal(3);

    res = await socketPromise;
    expect(res.lastMessage.sender).to.equal(BOO_BOT_ID);
    expect(res.lastMessage.text).to.equal(
      `${translateEmail({
        phrase: `We have compiled all the information associated with your Boo account. You can download it using the link below, valid for 7 days.`,
        locale: user0.locale,
      })}\n\nMOCK_SIGNED_URL`,
    );

    res = await socketPromise1;
    expect(res.lastMessage.sender).to.equal(BOO_BOT_ID);
    const expectedMessage = `${translateEmail({
      phrase: `We have compiled all the information associated with your Boo account. You can download it using the link below, valid for 7 days.`,
      locale: user1.locale,
    })}\n\nMOCK_SIGNED_URL`;
    expect(res.lastMessage.text).to.equal(expectedMessage);

    res = await socketPromise2;
    expect(res.lastMessage.sender).to.equal(BOO_BOT_ID);
    expect(res.lastMessage.text).to.equal(
      `${translateEmail({
        phrase: `We have compiled all the information associated with your Boo account. You can download it using the link below, valid for 7 days.`,
        locale: user2.locale,
      })}\n\n[Download Data](MOCK_SIGNED_URL)`,
    );

    await destroySocket(socket0);
    await destroySocket(socket1);
    await destroySocket(socket2);
  });

  it('should error if media download failed', async () => {
    let res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message').set('authorization', 1).send({
      user: '0',
      text: 'msg2',
    });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message/image').query({ recipient: 1 }).set('authorization', 0).attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/user/requestData').set('authorization', 0);
    expect(res.status).to.equal(200);

    // image download error should cause export failed
    res = await request(app).post('/v1/worker-data-export/exportData').set('authorization', 0);
    expect(res.status).to.equal(200);

    let history = await DataRequestHistory.find({});
    expect(history.length).to.equal(1);
    expect(history[0].status).to.equal('pending');
    expect(history[0].retryCount).to.equal(1);
    expect(history[0]).to.have.property('error');
    expect(history[0].processingTimeSec).to.not.equal(undefined);
    expect(history[0]).to.have.property('errors');
    expect(history[0].errors.length).to.equal(1);
    expect(history[0].errors[0]).to.have.property('errorAt');
    expect(history[0].errors[0]).to.have.property('error');
    expect(history[0].errors[0].error).to.include('message');

    res = await request(app).post('/v1/user/requestData').set('authorization', 1);
    expect(res.status).to.equal(200);

    // export will fail due to error in generating singed url
    cfsign.getSignedUrl.restore();
    sinon.stub(cfsign, 'getSignedUrl').throws(new Error('Failed to get signed URL'));
    res = await request(app).post('/v1/worker-data-export/exportData').set('authorization', 0);
    expect(res.status).to.equal(200);

    history = await DataRequestHistory.find({});
    expect(history.length).to.equal(2);
    expect(history[1].status).to.equal('pending');
    expect(history[1].retryCount).to.equal(1);
    expect(history[1]).to.have.property('error');
    expect(history[1]).to.have.property('errors');
    expect(history[1].errors.length).to.equal(1);
    expect(history[1].errors[0]).to.have.property('errorAt');
    expect(history[1].errors[0]).to.have.property('error');
    expect(history[1].errors[0].error).to.include('Failed to get signed URL');
  });

  it('add download link to ban notice', async () => {
    const clock = sinon.useFakeTimers({
      now: Date.now(),
      toFake: ['Date'],
    });

    let user = await User.findOne({ _id: '0' });
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    user = await User.findOne({ _id: '1' });
    expect(user.dataExportDownloadLink).to.equal(undefined);
    expect(user.dataExportDownloadLinkAt).to.equal(undefined);
    user.appVersion = '1.13.102';
    await user.save();

    // data request by user 1
    let res = await request(app).post('/v1/user/requestData').set('authorization', 1);
    expect(res.status).to.equal(200);

    // ban user 1
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({ user: '1', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    const ip = '***************';
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .set('X-Forwarded-For', ip);
    expect(res.status).to.equal(200);

    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
      policy: reportLib.banReasonToRuleMapping.scamming.policy,
      section: reportLib.banReasonToRuleMapping.scamming.section,
      rule: reportLib.banReasonToRuleMapping.scamming.rule,
    });

    res = await request(app).post('/v1/worker-data-export/exportData').set('authorization', 0);
    expect(res.status).to.equal(200);

    // should add download link
    user = await User.findOne({ _id: '1' });
    expect(user.dataExportDownloadLink).to.not.equal(undefined);
    expect(user.dataExportDownloadLinkAt).to.not.equal(undefined);

    // should show download link in ban notice
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .set('X-Forwarded-For', ip);
    expect(res.status).to.equal(200);

    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
      policy: reportLib.banReasonToRuleMapping.scamming.policy,
      section: reportLib.banReasonToRuleMapping.scamming.section,
      rule: reportLib.banReasonToRuleMapping.scamming.rule,
      dataDownloadLink: user.dataExportDownloadLink,
    });

    // fast forward 8 days
    clock.tick(8 * 24 * 60 * 60 * 1000);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .set('X-Forwarded-For', ip);
    expect(res.status).to.equal(200);

    // download link should be removed from ban notice after 7 days
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
      policy: reportLib.banReasonToRuleMapping.scamming.policy,
      section: reportLib.banReasonToRuleMapping.scamming.section,
      rule: reportLib.banReasonToRuleMapping.scamming.rule,
    });

    user = await User.findOne({ _id: '1' });
    expect(user.dataExportDownloadLink).to.equal(undefined);
    expect(user.dataExportDownloadLinkAt).to.equal(undefined);

    clock.restore();
  });
});

it('error route', async () => {
  res = await request(app)
    .post('/v1/worker-data-export/error');
  expect(res.status).to.equal(500);
})
