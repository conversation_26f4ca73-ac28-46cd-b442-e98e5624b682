const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const moment = require('moment');
const { app, validImagePath } = require('./common');
const userMiddleware = require('../middleware/user');
const actionLib = require('../lib/action');
const promptsLib = require('../lib/prompts');
const { validGoogleReceipt } = require('./iap');
const constants = require('../lib/constants');
const { DAILY_LIKES_LIMIT, DAILY_PROFILE_LIMIT, countryCodes } = require('../lib/constants');
const coinsConstants = require('../lib/coins-constants');
const User = require('../models/user');
const UserMetadata = require('../models/user-metadata');
const Action = require('../models/action');
const ExclusionList = require('../models/exclusion-list');
const ExclusionListRecalculation = require('../models/exclusion-list-recalculation');
const Interest = require('../models/interest');
const interestLib = require('../lib/interest');
const Message = require('../models/message');
const Story = require('../models/story');
const { moreAboutUserChoices } = require('../lib/moreAboutUser');
const locationLib = require('../lib/location');
const profilesLib = require('../lib/profiles-v3');
const { createQuestion } = require('../lib/social');
const basic = require('../lib/basic');
const geoip = require('geoip-lite');
const UsersWhoLiked = require('../models/users-who-liked')
const TopPicksExclusionList = require('../models/top-picks-exclusion-list')
const Question = require('../models/question')
const Comment = require('../models/comment')
const { DateTime } = require('luxon');
const chatLib = require('../lib/chat');
const mongoose = require('mongoose');

const msPerHour = 60 * 60 * 1000;

async function initUser(uid) {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', uid);
  expect(res.status).to.equal(200);

  // mock upload two pictures
  const user = await User.findOne({ _id: uid });
  user.pictures.push('picture0');
  user.pictures.push('picture1');
  user.scores.numPictures = 1;
  res = await user.save();

  res = await request(app)
    .put('/v1/user/personality')
    .set('authorization', uid)
    .send({
      mbti: 'ESTJ',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/relationshipStatus')
    .set('authorization', uid)
    .send({
      relationshipStatus: 'Single',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/datingSubPreferences')
    .set('authorization', uid)
    .send({
      datingSubPreferences: 'Short term fun',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/relationshipType')
    .set('authorization', uid)
    .send({
      relationshipType: 'Polyamorous',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/relationshipType')
    .set('authorization', uid)
    .send({
      relationshipType: 'Polyamorous',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/handle')
    .set('authorization', uid)
    .send({
      handle: `handle${uid}`,
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/gender')
    .set('authorization', uid)
    .send({ gender: 'female' });
  res = await request(app)
    .put('/v1/user/work')
    .set('authorization', uid)
    .send({ work: 'work' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', uid)
    .send({
      purpose: ['friends'],
      gender: ['female'],
      personality: ['ESTJ'],
    });
  res = await request(app)
    .put('/v1/user/birthday')
    .set('authorization', uid)
    .send({
      year: new Date().getFullYear() - 31,
      month: 1,
      day: 1,
    });
  // Honolulu, HI
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', uid)
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/profilePromptAnswers')
    .set('authorization', uid)
    .send({
      prompts: [
        { id: promptsLib.promptsArray[0].id, answer: `${uid}` },
      ],
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/autoFollowLikes')
    .set('authorization', uid)
    .send({
      autoFollowLikes: false,
    });
}

const createMatchingUserGeneral = async (uid, gender, disableLocation) => {
  let res = await request(app)
    .put('/v1/user/initApp')
    .send({ appVersion: '1.13.54' })
    .set('authorization', uid);
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', uid)
    .send({
      minAge: 18,
      maxAge: 200,
      dating: ['male', 'female', 'non-binary'],
      friends: ['male', 'female', 'non-binary'],
      distance: null,
      distance2: null,
      local: true,
      global: true,
      showUsersOutsideMyRange: true,
      sameCountryOnly: false,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/gender')
    .set('authorization', uid)
    .send({ gender: gender });
  expect(res.status).to.equal(200);


  res = await request(app)
    .put('/v1/user/birthday')
    .set('authorization', uid)
    .send({
      year: 1990,
      month: 8,
      day: 29
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/quizAnswers')
    .set('authorization', uid)
    .send({
      answers: {}
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', uid)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  if(!disableLocation){
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);
  }
};

describe('APP-895-V2 - Only super like visible', () => {
  beforeEach(async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', chatLib.BOO_BOT_ID);
    expect(res.status).to.equal(200);

    for (let i = 0; i <= 10; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: i % 2 === 0 ? '1.13.103' : '1.13.98' });
      expect(res.status).to.equal(200);

      let user = await User.findById(i);
      expect(user.appVersion).to.equal(i % 2 === 0 ? '1.13.103' : '1.13.98');
      expect(user.config.app_895_v2).to.equal(i % 2 === 0 ? true : undefined);
      if (i % 2 === 0) {
        user.verification.status = 'verified';
        await user.save();
      }
    }
  });

  it('creates or increments hourly like entry', async () => {
    let res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    const currentHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });
    let user = await User.findById('1');
    expect(user.metrics.numLikesReceivedHourly.size).to.equal(1);
    expect(user.metrics.numLikesReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(currentHourKey)).to.equal(1);
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(1);

    // send like from user 3, unverified, should not increase count
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 3)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    expect(user.metrics.numLikesReceivedHourly.size).to.equal(1);
    expect(user.metrics.numLikesReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(currentHourKey)).to.equal(1);
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(1);

    // send like from user 2, should increase count
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 2)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    expect(user.metrics.numLikesReceivedHourly.size).to.equal(1);
    expect(user.metrics.numLikesReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(currentHourKey)).to.equal(2);
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(2);

    // shadow banned users should not increase count
    user = await User.findById('4');
    user.shadowBanned = true;
    await user.save();

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 4)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    expect(user.metrics.numLikesReceivedHourly.size).to.equal(1);
    expect(user.metrics.numLikesReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(currentHourKey)).to.equal(2);
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(2);
  });

  it('removes likes older than 24h but keeps cutoff hour', async () => {
    let clock = sinon.useFakeTimers({
      now: Date.now(),
    });

    let res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    const initHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });
    let user = await User.findById('1');
    expect(user.metrics.numLikesReceivedHourly.size).to.equal(1);
    expect(user.metrics.numLikesReceivedHourly.has(initHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(initHourKey)).to.equal(1);
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(1);

    clock.tick(3600000); // 1 hour later

    user = await User.findById('2');
    user.numSuperLikesFree = 2;
    await user.save();

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 2)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    const oneHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });
    user = await User.findById('1');
    expect(user.metrics.numLikesReceivedHourly.size).to.equal(2);
    expect(user.metrics.numLikesReceivedHourly.has(initHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(initHourKey)).to.equal(1);
    expect(user.metrics.numLikesReceivedHourly.has(oneHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(oneHourKey)).to.equal(1);
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(2);

    clock.tick(3600000 * 22); // 22 hours later

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 4)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 6)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    const twentyThreeHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });
    user = await User.findById('1');
    expect(user.metrics.numLikesReceivedHourly.size).to.equal(3);
    expect(user.metrics.numLikesReceivedHourly.has(initHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(initHourKey)).to.equal(1);
    expect(user.metrics.numLikesReceivedHourly.has(oneHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(oneHourKey)).to.equal(1);
    expect(user.metrics.numLikesReceivedHourly.has(twentyThreeHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(twentyThreeHourKey)).to.equal(2);
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(4);

    clock.tick(3600000); // 1 hour later, initHourKey is now older than 24h but exists until next like count insert
    user = await User.findById('1');
    expect(user.metrics.numLikesReceivedHourly.size).to.equal(3);
    expect(user.metrics.numLikesReceivedHourly.has(initHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(initHourKey)).to.equal(1);
    expect(user.metrics.numLikesReceivedHourly.has(oneHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(oneHourKey)).to.equal(1);
    expect(user.metrics.numLikesReceivedHourly.has(twentyThreeHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(twentyThreeHourKey)).to.equal(2);
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(3); // initHourKey is older than 24h

    // new like should remove initHourKey
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 8)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    const currentHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });
    user = await User.findById('1');
    expect(user.metrics.numLikesReceivedHourly.size).to.equal(3); // initHourKey was removed
    expect(user.metrics.numLikesReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(currentHourKey)).to.equal(1);
    expect(user.metrics.numLikesReceivedHourly.has(oneHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(oneHourKey)).to.equal(1);
    expect(user.metrics.numLikesReceivedHourly.has(twentyThreeHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(twentyThreeHourKey)).to.equal(2);
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(4);
    expect(user.metrics.numLikesReceivedHourly.has(initHourKey)).to.equal(false);
    expect(user.metrics.numLikesReceivedHourly.get(initHourKey)).to.equal(undefined);

    // sending like/superlike/directMessage from same user again should not increase count
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 8)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 2)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    user.premiumExpiration = new Date(Date.now() + 365 * 24 * 3600 * 1000);
    await user.save();

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: `1`,
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(4);

    clock.restore();
  });

  it('handles race condition when multiple likes arrive at the same time', async () => {
    const currentHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });

    // send 5 concurrent likes from different verified users
    const senders = [2, 4, 6, 8, 10];
    await Promise.all(
      senders.map(uid =>
        request(app)
          .patch('/v1/user/sendLike')
          .set('authorization', uid)
          .send({ user: '1' })
      )
    );

    // verify count is 5 (not 1, not corrupted by race)
    user = await User.findById('1');
    expect(user.metrics.numLikesReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numLikesReceivedHourly.get(currentHourKey)).to.equal(5);
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(5);
  });

  it('sets onlySuperLikeVisible = true when all conditions met', async () => {
    for (let uid = 0; uid < 3; uid++) {
      await initUser(uid);
    }

    // add 5 likes in last 24 hours
    for (let i = 2; i <= 10; i += 2) {
      let res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', i)
        .send({ user: '1' });
      expect(res.status).to.equal(200);
    }

    // condition setup: Top Soul
    let user = await User.findById('1');
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(5);
    user.scores.likeRatio = 0.70;
    user.metrics.numActionsReceived = 30;
    await user.save();

    user = await User.findById('0');
    expect(user.metrics.seenSuperLikeOnlyCandidate).to.equal(undefined);
    user.gender = 'male';
    user.preferences.friends = [];
    user.preferences.dating = ['female'];
    await user.save();
    expect(user.config.app_895_v2).to.equal(true);

    let res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.app_895).to.equal(true);
    expect(res.body.app_895_v2).to.equal(true);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].onlySuperLikeVisible).to.equal(true);
    expect(res.body.profiles[0].tags).to.include('Top Soul');

    expect(res.body.profiles[1]._id).to.equal('2');
    expect(res.body.profiles[1].onlySuperLikeVisible).to.equal(undefined);
    expect(res.body.profiles[1].tags).to.not.include('Top Soul');

    user = await User.findById('0');
    expect(user.metrics.seenSuperLikeOnlyCandidate).to.equal(undefined);

    res = await request(app)
      .patch('/v1/user/profileView')
      .set('authorization', 0)
      .query({ user: '2' });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.metrics.seenSuperLikeOnlyCandidate).to.equal(undefined);

    res = await request(app)
      .patch('/v1/user/profileView')
      .set('authorization', 0)
      .query({ user: '1', source: 'matches' });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.metrics.seenSuperLikeOnlyCandidate).to.equal(true);
  });

  it('test super like only qualified profile seen metric update', async () => {
    for (let uid = 0; uid < 3; uid++) {
      await initUser(uid);
    }

    // add 5 likes in last 24 hours
    for (let i = 6; i <= 10; i++) {
      let user = await User.findById(i);
      user.verification.status = 'verified';
      await user.save();

      let res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', i)
        .send({ user: '1' });
      expect(res.status).to.equal(200);
    }

    // condition setup: Top Soul
    let user = await User.findById('1');
    expect(chatLib.getLikesReceivedInLast24Hours(user)).to.equal(5);
    user.scores.likeRatio = 0.70;
    user.metrics.numActionsReceived = 30;
    await user.save();

    user = await User.findById('0');
    expect(user.metrics.seenSuperLikeOnlyCandidate).to.equal(undefined);
    user.gender = 'male';
    user.preferences.friends = [];
    user.preferences.dating = ['female'];
    await user.save();

    let res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].onlySuperLikeVisible).to.equal(true);
    expect(res.body.profiles[0].tags).to.include('Top Soul');

    expect(res.body.profiles[1]._id).to.equal('2');
    expect(res.body.profiles[1].onlySuperLikeVisible).to.equal(undefined);
    expect(res.body.profiles[1].tags).to.not.include('Top Soul');

    // view added from profile details, should not set metric
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.metrics.seenSuperLikeOnlyCandidate).to.equal(undefined);

    // set condition for user 2 with config false
    user = await User.findById('2');
    user.gender = 'male';
    user.config.app_895_v2 = false;
    user.preferences.friends = [];
    user.preferences.dating = ['female'];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/profileView')
      .set('authorization', 2)
      .query({ user: '1', source: 'matches' });
    expect(res.status).to.equal(200);

    user = await User.findById('2');
    expect(user.metrics.seenSuperLikeOnlyCandidate).to.equal(true);
  });
});

describe('daily profiles', () => {
  const numUsers = 2;

  const userProfile0 = {
    _id: '0',
    age: 31,
    crown: false,
    hideQuestions: false,
    hideComments: false,
    description: '',
    location: 'Honolulu, HI 🇺🇸',
    nearby: true,
    teleport: false,
    education: '',
    work: 'work',
    enneagram: '1w9',
    prompts: [{
      id: promptsLib.promptsArray[0].id,
      prompt: promptsLib.promptsArray[0].prompt,
      answer: '0',
    }],
    interests: [],
    interestNames: [],
    firstName: '',
    gender: 'female',
    handle: 'handle0',
    personality: { mbti: 'ESTJ', avatar: 'Executive' },
    relationshipStatus: "Single",
    datingSubPreferences: "Short term fun",
    relationshipType: "Polyamorous",
    pictures: ['picture0', 'picture1'],
    profilePicture: 'picture0',
    preferences: { purpose: ['friends'] },
    horoscope: 'Capricorn',
    karma: 0,
    numFollowers: 0,
    verified: false,
    verificationStatus: 'unverified',
    stories: [],
  };
  const userProfile1 = {
    _id: '1',
    age: 31,
    crown: false,
    hideQuestions: false,
    hideComments: false,
    description: '',
    location: 'Kailua, HI 🇺🇸',
    nearby: true,
    teleport: true,
    education: '',
    work: 'work',
    prompts: [{
      id: promptsLib.promptsArray[0].id,
      prompt: promptsLib.promptsArray[0].prompt,
      answer: '1',
    }],
    interests: [],
    interestNames: [],
    firstName: '',
    gender: 'female',
    handle: 'handle1',
    personality: { mbti: 'ESTJ', avatar: 'Executive' },
    relationshipStatus: "Single",
    datingSubPreferences: "Short term fun",
    relationshipType: "Polyamorous",
    pictures: ['picture0', 'picture1'],
    profilePicture: 'picture0',
    preferences: { purpose: ['friends'] },
    horoscope: 'Capricorn',
    karma: 0,
    numFollowers: 0,
    verified: false,
    verificationStatus: 'unverified',
    stories: [],
  };

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      await initUser(uid);
    }

    // teleport for user 1
    user = await User.findOne({ _id: 1 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .put('/v1/teleport/location')
      .set('authorization', 1)
      .send({
        latitude: 21.40,
        longitude: -157.74,
      });
    expect(res.status).to.equal(200);

    // enneagram for user 0
    res = await request(app)
      .put('/v1/user/enneagram')
      .set('authorization', 0)
      .send({ enneagram: '1w9' });
    expect(res.status).to.equal(200);
  });

  /*
  it('local profiles', async function() {

    // make everyone like the first user
    for (let uid = 1; uid < numUsers; uid++) {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', uid)
        .send({
          "user": "0",
        })
    }
    await User.ensureIndexes();

    // new user should see most liked user first
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', numUsers)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
    expect(res.body.profiles[0]).to.eql(userProfile0);

    // non-new users should not see most liked user first
    user = await User.findById(numUsers)
    user.createdAt = new Date(1990,1,1)
    savedUser = await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', numUsers)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
    assert(res.body.profiles[0]._id != '0');
  });

  it('backwards compatibility', async function() {

    // make everyone like the first user
    for (let uid = 1; uid < numUsers; uid++) {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', uid)
        .send({
          "user": "0",
        })
    }
    await User.ensureIndexes();

    user = await User.findById('0')
    user.metrics = undefined;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', numUsers)
    expect(res.status).to.equal(200);
    expect(res.body.profiles[0]).to.eql(userProfile0);
  });
  */

it('dating sub preferences and relationship status field for daily profiles', async () => {
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]).to.eql(userProfile1);
});

it('dating sub preferences and relationship status field for top picks', async () => {
  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]).to.eql(userProfile1);
});

  it('cached', async () => {
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');

    // wait for async operation to complete
    await new Promise((r) => setTimeout(r, 500));

    user = await User.findById('1');
    user.profileModifiedAt = new Date(2000, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
  });

  it('deleted account should not appear', async () => {
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');

    user = await User.findOne({ _id: 1 });
    user.deletionRequestDate = new Date(2000, 1, 1);
    await user.save();

    res = await request(app)
      .post('/v1/worker/deleteUsers')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // wait for async operation to complete
    await new Promise((r) => setTimeout(r, 300));

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
  });

  it('hiding location should hide user', async () => {
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();

    res = await request(app)
      .put('/v1/user/hideLocation')
      .set('authorization', 0)
      .send({ hideLocation: true });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/hideLocation')
      .set('authorization', 0)
      .send({ hideLocation: false });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
  });

  it('banning user should hide user', async () => {
    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
  });

  it('enneagram filter', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.enneagrams).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile0);

    // invalid input
    res = await request(app)
      .patch('/v1/user/preferences/enneagrams')
      .set('authorization', 0)
      .send({
        enneagrams: '1w2',
      });
    expect(res.status).to.equal(422);
    res = await request(app)
      .patch('/v1/user/preferences/enneagrams')
      .set('authorization', 0)
      .send({
        enneagrams: ['invalid'],
      });
    expect(res.status).to.equal(422);

    // user 0 - filter
    res = await request(app)
      .patch('/v1/user/preferences/enneagrams')
      .set('authorization', 0)
      .send({
        enneagrams: ['1w2'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.enneagrams).to.eql(['1w2']);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 1 adds enneagram
    res = await request(app)
      .put('/v1/user/enneagram')
      .set('authorization', 1)
      .send({
        enneagram: '1w2',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - clear filter
    res = await request(app)
      .patch('/v1/user/preferences/enneagrams')
      .set('authorization', 0)
      .send({
        enneagrams: null,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.enneagrams).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
  });

  it('bio length filter', async () => {
    constants.throttleScoreUpdates.restore();
    sinon.stub(constants, 'throttleScoreUpdates').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.bioLength).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile0);

    // user 0 - filter
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        bioLength: 5,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.bioLength).to.equal(5);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 1 adds bio
    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', 1)
      .send({
        description: '12345',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - clear filter
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        bioLength: 0,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.bioLength).to.equal(0);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
  });

  it('language filter', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.languages).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile0);

    // invalid input
    res = await request(app)
      .patch('/v1/user/preferences/languages')
      .set('authorization', 0)
      .send({
        languages: 'en',
      });
    expect(res.status).to.equal(422);
    res = await request(app)
      .patch('/v1/user/preferences/languages')
      .set('authorization', 0)
      .send({
        languages: ['invalid'],
      });
    expect(res.status).to.equal(422);

    // user 0 - filter
    res = await request(app)
      .patch('/v1/user/preferences/languages')
      .set('authorization', 0)
      .send({
        languages: ['en'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.languages).to.eql(['en']);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 1 adds language
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 1)
      .send({
        languages: ['en'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - clear filter
    res = await request(app)
      .patch('/v1/user/preferences/languages')
      .set('authorization', 0)
      .send({
        languages: null,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.languages).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
  });

  it('horoscope filter', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.horoscopes).to.eql();

    // invalid input
    res = await request(app)
      .patch('/v1/user/preferences/horoscopes')
      .set('authorization', 0)
      .send({
        horoscopes: 'Aries',
      });
    expect(res.status).to.equal(422);
    res = await request(app)
      .patch('/v1/user/preferences/horoscopes')
      .set('authorization', 0)
      .send({
        horoscopes: ['invalid'],
      });
    expect(res.status).to.equal(422);

    // user 0 - filter
    res = await request(app)
      .patch('/v1/user/preferences/horoscopes')
      .set('authorization', 0)
      .send({
        horoscopes: ['Aries'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.horoscopes).to.eql(['Aries']);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    // admin changes birthday to Aries
    user = await User.findOne({ _id: 1 });
    user.admin = true;
    user.adminPermissions = { all: true };
    res = await user.save();

    res = await request(app)
      .put('/v1/admin/userBirthday')
      .set('authorization', 1)
      .send({
        user: '1',
        year: new Date().getFullYear() - 31,
        month: 4,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // test horoscope calculation
    var user = await User.findOne({ _id: 1 });
    user.horoscope = undefined;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    await actionLib.calculateHoroscopeAllUsers();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - clear filter
    res = await request(app)
      .patch('/v1/user/preferences/horoscopes')
      .set('authorization', 0)
      .send({
        horoscopes: null,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.horoscopes).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
  });

  it('relationship status filter', async () => {
    //check preferences for relationship status
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.relationshipStatus).to.eql();

    //no filter of relationship status set need to return 1 data for both users
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile1);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile0);

    // invalid filter set
    res = await request(app)
      .patch('/v1/user/preferences/relationshipStatus')
      .set('authorization', 0)
      .send({
        relationshipStatus: 'Single',
      });
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/user/preferences/relationshipStatus')
      .set('authorization', 0)
      .send({
        relationshipStatus: ['invalid'],
      });
    expect(res.status).to.equal(422);

    // user 0 - adds relationship status filter to married
    res = await request(app)
      .patch('/v1/user/preferences/relationshipStatus')
      .set('authorization', 0)
      .send({
        relationshipStatus: ['Married'],
      });
    expect(res.status).to.equal(200);

    //no users return as there is no user with Married status
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.relationshipStatus).to.eql(['Married']);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/relationshipStatus')
      .set('authorization', 1)
      .send({
        relationshipStatus: null,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.relationshipStatus).to.eql();

    res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      showUnspecified: {
        relationshipStatus: true
      },
    });
    expect(res.status).to.equal(200);

    res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    //restoring back to single
    res = await request(app)
    .put('/v1/user/relationshipStatus')
    .set('authorization', 1)
    .send({
      relationshipStatus: 'Single',
    });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    //as user 1 has not set filter user 0 is returned
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 1 changes relationship status to married
    res = await request(app)
      .put('/v1/user/relationshipStatus')
      .set('authorization', 1)
      .send({
        relationshipStatus: 'Married',
      });
    expect(res.status).to.equal(200);

    //one user return as there status is set to married for user 1
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    //as user 1 has not set filter user 0 is returned
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - clear filter
    res = await request(app)
      .patch('/v1/user/preferences/relationshipStatus')
      .set('authorization', 0)
      .send({
        relationshipStatus: null,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.relationshipStatus).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

     // user 1 relationship status back to single
     res = await request(app)
     .put('/v1/user/relationshipStatus')
     .set('authorization', 1)
     .send({
       relationshipStatus: 'Single',
     });
   expect(res.status).to.equal(200);

     // user 0 and 1 clearing filter
     res = await request(app)
     .patch('/v1/user/preferences/relationshipStatus')
     .set('authorization', 0)
     .send({
       relationshipStatus: null,
     });
   expect(res.status).to.equal(200);

   res = await request(app)
   .patch('/v1/user/preferences/relationshipStatus')
   .set('authorization', 1)
   .send({
     relationshipStatus: null,
   });

 expect(res.status).to.equal(200);
  });

  it('dating sub preferences filter', async () => {
    //check preferences for dating sub preferences
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.datingSubPreferences).to.eql();

    //no filter of dating sub preferences set need to return 1 data for both users
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile1);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile0);

    // invalid filter set
    res = await request(app)
      .patch('/v1/user/preferences/datingSubPreferences')
      .set('authorization', 0)
      .send({
        datingSubPreferences: 'Short term fun',
      });
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/user/preferences/datingSubPreferences')
      .set('authorization', 0)
      .send({
        datingSubPreferences: ['invalid'],
      });
    expect(res.status).to.equal(422);

    // user 0 - adds dating sub preferences filter to Long term partner
    res = await request(app)
      .patch('/v1/user/preferences/datingSubPreferences')
      .set('authorization', 0)
      .send({
        datingSubPreferences: ['Long term partner'],
      });
    expect(res.status).to.equal(200);

    //no users return as there is no user with Long term partner status
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.datingSubPreferences).to.eql(['Long term partner']);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    //as user 1 has not set filter user 0 is returned
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 1 changes dating sub preferences to Long term partner
    res = await request(app)
      .put('/v1/user/datingSubPreferences')
      .set('authorization', 1)
      .send({
        datingSubPreferences: 'Long term partner',
      });
    expect(res.status).to.equal(200);

    //one user return as there status is set to Long term partner for user 1
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    //as user 1 has not set filter user 0 is returned
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - clear filter
    res = await request(app)
      .patch('/v1/user/preferences/datingSubPreferences')
      .set('authorization', 0)
      .send({
        datingSubPreferences: null,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.datingSubPreferences).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

     // user 1 dating sub preferences back to Short term fun
     res = await request(app)
     .put('/v1/user/datingSubPreferences')
     .set('authorization', 1)
     .send({
       datingSubPreferences: 'Short term fun',
     });
   expect(res.status).to.equal(200);

        // user 0 and 1 clearing filter
        res = await request(app)
        .patch('/v1/user/preferences/datingSubPreferences')
        .set('authorization', 0)
        .send({
          datingSubPreferences: null,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
      .patch('/v1/user/preferences/datingSubPreferences')
      .set('authorization', 1)
      .send({
        datingSubPreferences: null,
      });
  });

  it('relationship type filter', async () => {
    //check preferences for relationship type
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.relationshipType).to.eql();

    res = await request(app)
      .put('/v1/user/relationshipType')
      .set('authorization', 1)
      .send({
        relationshipType: null,
      });
    expect(res.status).to.equal(200);

    delete userProfile1.relationshipType

    //no filter of relationship type set need to return 1 data for both users
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile1);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile0);

    // invalid filter set
    res = await request(app)
      .patch('/v1/user/preferences/relationshipType')
      .set('authorization', 0)
      .send({
        relationshipType: 'Polyamorous',
      });
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/user/preferences/relationshipType')
      .set('authorization', 0)
      .send({
        relationshipType: ['invalid'],
      });
    expect(res.status).to.equal(422);

    // user 0 - adds relationship type filter to Monogamous
    res = await request(app)
      .patch('/v1/user/preferences/relationshipType')
      .set('authorization', 0)
      .send({
        relationshipType: ['Monogamous', 'Polyamorous', 'Open relationship'],
      });
    expect(res.status).to.equal(200);

    //no users return as there is no user with Monogamous status
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.relationshipType).to.eql(['Monogamous', 'Polyamorous', 'Open relationship']);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    //as user 1 has not set filter user 0 is returned
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 1 changes relationship type to Monogamous
    res = await request(app)
      .put('/v1/user/relationshipType')
      .set('authorization', 1)
      .send({
        relationshipType: 'Monogamous',
      });
    expect(res.status).to.equal(200);

    //one user return as there status is set to Monogamous for user 1
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    //as user 1 has not set filter user 0 is returned
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - clear filter
    res = await request(app)
      .patch('/v1/user/preferences/relationshipType')
      .set('authorization', 0)
      .send({
        relationshipType: null,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.relationshipType).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

     // user 1 relationship type back to Polyamorous
     res = await request(app)
     .put('/v1/user/relationshipType')
     .set('authorization', 1)
     .send({
       relationshipType: 'Polyamorous',
     });
   expect(res.status).to.equal(200);

   userProfile1.relationshipType = 'Polyamorous';

     // user 0 and 1 clearing filter
     res = await request(app)
     .patch('/v1/user/preferences/relationshipType')
     .set('authorization', 0)
     .send({
       relationshipType: null,
     });
   expect(res.status).to.equal(200);

   res = await request(app)
    .patch('/v1/user/preferences/relationshipType')
    .set('authorization', 1)
    .send({
      relationshipType: null,
    });
    expect(res.status).to.equal(200);
  });

  it('sexuality status filter', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.sexuality).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile1);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile0);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        sexuality: 'invalid',
      });
    expect(res.status).to.equal(422);

    res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      sexuality: 'pansexual',
    });
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        sexuality: ['pansexual'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.sexuality).to.eql(['pansexual']);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    res = await request(app)
      .put('/v1/user/sexuality')
      .set('authorization', 1)
      .send({
        sexuality: 'pansexual',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        sexuality: null,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.sexuality).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    res = await request(app)
      .put('/v1/user/sexuality')
      .set('authorization', 1)
      .send({
        sexuality: 'other',
      });
   expect(res.status).to.equal(200);

   res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);

   res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 1)
    .send({
      sexuality: null,
    });
    expect(res.status).to.equal(200);
  });

  it('interest names filter', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.interestNames).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile0);

    // invalid input
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interestNames: 'kpop',
      });
    expect(res.status).to.equal(422);
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['invalid'],
      });
    expect(res.status).to.equal(422);

    // user 0 - filter kpop
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['kpop', 'kpop'],
      });
    expect(res.status).to.equal(200);

    // duplicate is filtered out
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.interestNames).to.eql(['kpop']);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 1 adds kpop interest
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 1)
      .send({
        interestNames: ['kpop'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - clear filter
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interestNames: null,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.interestNames).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - filter latin
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['latin'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.interestNames).to.eql(['latin']);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - filter kpop and latin
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['kpop', 'latin'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.interestNames).to.eql(['kpop', 'latin']);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
  });

  it('interests filter', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.interests).to.eql();
    kpopId = res.body.interests[0]._id;
    latinId = res.body.interests[1]._id;

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]).to.eql(userProfile0);

    // invalid input
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interests: kpopId,
      });
    expect(res.status).to.equal(422);
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interests: ['invalid'],
      });
    expect(res.status).to.equal(422);
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interests: [kpopId, kpopId],
      });
    expect(res.status).to.equal(422);

    // user 0 - filter kpop
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interests: [kpopId],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.interests).to.eql([kpopId]);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 1 adds kpop interest
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 1)
      .send({
        interestIds: [kpopId],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - clear filter
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interests: null,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.interests).to.eql();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - filter latin
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interests: [latinId],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.interests).to.eql([latinId]);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 - filter kpop and latin
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interests: [kpopId, latinId],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.interests).to.eql([kpopId, latinId]);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
  });

  it('keywords preference filter', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.keywords).to.eql([]);

    // error when keywords not an array
    res = await request(app)
      .patch('/v1/user/preferences/keywords')
      .set('authorization', 0)
      .send({
        keywords: 'text',
      });
    expect(res.status).to.equal(422);

    // error when keywords contain spaces
    res = await request(app)
      .patch('/v1/user/preferences/keywords')
      .set('authorization', 0)
      .send({
        keywords: 'text data',
      });
    expect(res.status).to.equal(422);

    // accepts punctuations in between
    res = await request(app)
      .patch('/v1/user/preferences/keywords')
      .set('authorization', 0)
      .send({
        keywords: ['tex-t'],
      });
    expect(res.status).to.equal(200);

    // ignores all trailing and leading punctutations
    // does not error on recieving special characters but does not update the db if entirely consists of punctuations
    // accepts punctuations in between
    const punctuations = '!"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~';
    res = await request(app)
      .patch('/v1/user/preferences/keywords')
      .set('authorization', 0)
      .send({
        keywords: [punctuations, `${punctuations}boo${punctuations}world${punctuations}`],
      });
    expect(res.status).to.equal(200);
    expect(res.body.keywords.length === 1);
    expect(res.body.keywords[0]).to.equal(`boo${punctuations}world`);

    // succeed on setting empty
    res = await request(app)
      .patch('/v1/user/preferences/keywords')
      .set('authorization', 0)
      .send({
        keywords: [],
      });
    expect(res.status).to.equal(200);

    // verify preference for no change as already empty
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.keywords).to.eql([]);

    // succeed on correct input
    res = await request(app)
      .patch('/v1/user/preferences/keywords')
      .set('authorization', 0)
      .send({
        keywords: ['text'],
      });
    expect(res.status).to.equal(200);

    // preference updated
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.keywords).to.eql(['text']);

    // create user 1
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    // no daily profiles
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    const keyValues = {
      work: 'my ...best... work',
      description: 'my ...best... description',
      education: 'my ...best... education',
    };

    for (key in keyValues) {
      // User 1 updates {key}
      res = await request(app)
        .put(`/v1/user/${key}`)
        .set('authorization', 1)
        .send({
          [key]: keyValues[key],
        });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    // update user0 keywords preference with multiple keywords

    res = await request(app)
      .patch('/v1/user/preferences/keywords')
      .set('authorization', 0)
      .send({
        keywords: ['text', 'my'],
      });
    expect(res.status).to.equal(200);

    // find results containing any of the keywords
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user matches for strings between punctuations
    res = await request(app)
      .patch('/v1/user/preferences/keywords')
      .set('authorization', 0)
      .send({
        keywords: ['work', 'best'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // succeed on correct input (undefined) removing preference
    res = await request(app)
      .patch('/v1/user/preferences/keywords')
      .set('authorization', 0)
      .send({});
    expect(res.status).to.equal(200);

    // preference updated
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.keywords).to.eql([]);

    // search for text in any of description, work and education fields
    for (key in keyValues) {
      // update user0 keywords preference with only {key}
      res = await request(app)
        .patch('/v1/user/preferences/keywords')
        .set('authorization', 0)
        .send({
          keywords: [key],
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(1);

      // search should be case-insensitive
      res = await request(app)
        .patch('/v1/user/preferences/keywords')
        .set('authorization', 0)
        .send({
          keywords: [key.toUpperCase()],
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(1);

      // empty user1 {key}
      res = await request(app)
        .put(`/v1/user/${key}`)
        .set('authorization', 1)
        .send({ [key]: '' });
      expect(res.status).to.equal(200);

      // no daily profiles
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(0);
    }

    // search prompts
    res = await request(app)
      .patch('/v1/user/preferences/keywords')
      .set('authorization', 0)
      .send({
        keywords: ['prompt'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/profilePromptAnswers')
      .set('authorization', 1)
      .send({
        prompts: [
          { id: promptsLib.promptsArray[0].id, answer: 'my' },
          { id: promptsLib.promptsArray[1].id, answer: 'prompt answer' },
        ],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
  });

  for (const key in moreAboutUserChoices) {
    it(`preferences - ${key} filter`, async () => {
    // create user 1
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1)
        .send({ appVersion: '1.11.45' });
      expect(res.status).to.equal(200);

      // user 1 found without filters
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(1);

      // set filter
      res = await request(app)
        .put(`/v1/user/preferences/${key}`)
        .set('authorization', 0)
        .send({
          values: [moreAboutUserChoices[key][0]],
        });
      expect(res.status).to.equal(200);

      // no profiles matching filter
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(0);

      // set user 1 moreAboutUser - {key}
      res = await request(app)
        .put(`/v1/user/moreAboutUser/${key}`)
        .set('authorization', 1)
        .send({
          [key]: moreAboutUserChoices[key][0],
        });
      expect(res.status).to.equal(200);

      // now profile shown in filter
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(1);

      // set multiple filters
      res = await request(app)
        .put(`/v1/user/preferences/${key}`)
        .set('authorization', 0)
        .send({
          values: [moreAboutUserChoices[key][0], moreAboutUserChoices[key][1]],
        });
      expect(res.status).to.equal(200);

      // set user 1 moreAboutUser - {key} with other value
      res = await request(app)
        .put(`/v1/user/moreAboutUser/${key}`)
        .set('authorization', 1)
        .send({
          [key]: moreAboutUserChoices[key][1],
        });
      expect(res.status).to.equal(200);

      // user 1 still matches
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(1);
    });
  }
});

describe('looking for dating/friends filter', () => {
  const numUsers = 2;

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);
      res = await request(app)
        .put('/v1/user/handle')
        .set('authorization', uid)
        .send({
          handle: `handle${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          gender: [
            'female',
          ],
          personality: [
            'ISTP',
          ],
        });
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'female' });
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: 1990,
          month: 1,
          day: 1,
        });
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          answers: {},
        });
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          longitude: 0,
          latitude: 51,
        });

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', uid)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);
    }
    await User.ensureIndexes();
  });

  it('separate gender preferences for dating/friends', async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.10.17' });
      expect(res.status).to.equal(200);
    }

    // preferences not compatible
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        friends: [],
        dating: ['female'],
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        friends: ['female'],
        dating: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: 'handle1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql([]);
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 1)
      .query({ handle: 'handle0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql([]);
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: 'handle0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['dating']);
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 1)
      .query({ handle: 'handle1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['dating', 'friends']);

    // friends preferences compatible
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        friends: ['female', 'male'],
        dating: ['female'],
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        friends: ['female'],
        dating: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['friends'] });
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['friends'] });

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: 'handle1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['friends']);
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 1)
      .query({ handle: 'handle0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['friends']);
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: 'handle0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['dating', 'friends']);
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 1)
      .query({ handle: 'handle1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['dating', 'friends']);

    // friends + dating preferences compatible
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        friends: ['female', 'male'],
        dating: ['female'],
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        friends: ['female'],
        dating: ['female', 'male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating', 'friends'] });
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating', 'friends'] });

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: 'handle1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['dating', 'friends']);
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 1)
      .query({ handle: 'handle0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['dating', 'friends']);
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: 'handle0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['dating', 'friends']);
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 1)
      .query({ handle: 'handle1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['dating', 'friends']);

    // backwards compatibility
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.10.16' });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 1 });
    user.preferences.dating = undefined;
    user.preferences.friends = undefined;
    res = await user.save();

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        gender: ['female'],
        purpose: ['friends'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['friends'] });
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating', 'friends'] });

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: 'handle1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['friends']);
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 1)
      .query({ handle: 'handle0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['dating', 'friends']);
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: 'handle0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['dating', 'friends']);
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 1)
      .query({ handle: 'handle1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.purpose).to.eql(['friends']);
  });

  it('user 0: dating/friends, user 1: dating/friends', async () => {
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        purpose: [
          'dating',
          'friends',
        ],
      });
    expect(res.status).to.equal(200);

    // both users should see each other
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating', 'friends'] });
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating', 'friends'] });
  });

  it('user 0: dating/friends, user 1: dating', async () => {
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        purpose: [
          'dating',
        ],
      });
    expect(res.status).to.equal(200);

    // both users should see each other
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating'] });
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating', 'friends'] });
  });

  it('user 0: dating/friends, user 1: friends', async () => {
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        purpose: [
          'friends',
        ],
      });
    expect(res.status).to.equal(200);

    // both users should see each other
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['friends'] });
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating', 'friends'] });
  });

  it('user 0: dating, user 1: dating', async () => {
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        purpose: [
          'dating',
        ],
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        purpose: [
          'dating',
        ],
      });
    expect(res.status).to.equal(200);

    // both users should see each other
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating'] });
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating'] });
  });

  it('user 0: friends, user 1: friends', async () => {
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        purpose: [
          'friends',
        ],
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        purpose: [
          'friends',
        ],
      });
    expect(res.status).to.equal(200);

    // both users should see each other
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['friends'] });
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['friends'] });

    user = await User.findById('0');
    expect(user.events.out_of_souls).to.equal();
    user = await User.findById('1');
    expect(user.events.out_of_souls).to.equal();
  });

  it('user 0: dating, user 1: friends', async () => {
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        purpose: [
          'dating',
        ],
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        purpose: [
          'friends',
        ],
      });
    expect(res.status).to.equal(200);

    // users should not see each other
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    expect(res.body.noUsersNearby).to.equal(true);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    expect(res.body.noUsersNearby).to.equal(true);

    user = await User.findById('0');
    expect(user.events.out_of_souls).to.equal(1);
    user = await User.findById('1');
    expect(user.events.out_of_souls).to.equal(1);
  });

  it('Backwards compatibility - user 0: undefined, user 1: undefined', async () => {
    user = await User.findOne({ _id: 0 });
    user.preferences.purpose = undefined;
    res = await user.save();
    user = await User.findOne({ _id: 1 });
    user.preferences.purpose = undefined;
    res = await user.save();

    // both users should see each other
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating', 'friends'] });
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating', 'friends'] });
  });

  it('Backwards compatibility - user 0: undefined, user 1: friends', async () => {
    user = await User.findOne({ _id: 0 });
    user.preferences.purpose = undefined;
    res = await user.save();
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        purpose: [
          'friends',
        ],
      });
    expect(res.status).to.equal(200);

    // both users should see each other
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['friends'] });
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].preferences).to.eql({ purpose: ['dating', 'friends'] });
  });
});

describe('daily profiles reset time', () => {
  const numUsers = DAILY_PROFILE_LIMIT + 3;
  let clock;

  // January 1, 2017 12:00:00 AM UTC
  initDateMs = 1483228800000;

  beforeEach(async () => {
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          gender: [
            'female',
          ],
          personality: [
            'ISTP',
          ],
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: 1990,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          answers: {},
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          longitude: 0,
          latitude: 51,
        });
      expect(res.status).to.equal(200);

      const user = await User.findOne({ _id: uid });
      user.createdAt = 0;
      user.viewableInDailyProfiles = true;
      res = await user.save();
    }
    await User.ensureIndexes();
  });

  afterEach(() => {
    clock.restore();
  });

  /*
  it('reset time', async () => {
    for (let i = 0; i < 3; i++) {
      user = await User.findOne({ _id: i });
      user.metrics.currentDayResetTime = null;
      await user.save();
    }

    // User 0 - UTC time noon (default when no timezone set)
    // User 1 - Macau time noon (UTC+8)
    // User 2 - Asia/Kamchatka (UTC+12)
    res = await request(app)
      .put('/v1/user/deviceInfo')
      .set('authorization', 0)
      .send({
        appVersion: '1.7.0',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/deviceInfo')
      .set('authorization', 1)
      .send({
        timezone: 'Asia/Macau',
        appVersion: '1.7.0',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/deviceInfo')
      .set('authorization', 2)
      .send({
        timezone: 'Asia/Kamchatka',
        appVersion: '1.6.0',
      });
    expect(res.status).to.equal(200);

    // Initially, all users should get recommendations
    for (let uid = 0; uid < 3; uid++) {
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

      // Like all the recommendations
      profiles = res.body.profiles;
      for (let i = 0; i < profiles.length; i++) {
        res = await request(app)
          .patch('/v1/user/pass')
          .set('authorization', uid)
          .send({
            user: profiles[i]._id,
          });
        expect(res.status).to.equal(200);
      }

      // After acting on all recommendations, user should get no more
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      console.log(res.body);
      expect(res.status).to.equal(200);
      expect(res.body.dailyLimitExceeded).to.equal(true);
    }

    // After 11 hours 30 minutes, user 0 and 2 should still not get login reward (11:30 UTC)
    // User 1 should have recs (19:30 Asia/Macau)
    clock.tick(11.5 * msPerHour);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    console.log(res.body);
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal(true);
    // expect(res.body.timer).to.equal(new Date(initDateMs + 12 * msPerHour).toISOString());
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal(undefined);
    expect(res.body.profiles.length).to.be.gt(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 2);
    console.log(res.body);
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal(true);
    // expect(res.body.timer).to.equal(new Date(initDateMs + 24 * msPerHour).toISOString());

    // After 29 minutes 55 seconds, users 0 and 1 should have recs (11:59:55 UTC)
    clock.tick(2 * msPerHour - 5000);
    for (let uid = 0; uid < 2; uid++) {
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      expect(res.body.dailyLimitExceeded).to.equal(undefined);
      expect(res.body.profiles.length).to.be.gt(0);
    }
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 2);
    console.log(res.body);
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal(true);
    // expect(res.body.timer).to.equal(new Date(initDateMs + 24 * msPerHour).toISOString());

    // After 20 more hours, all users should have recs
    clock.tick(20 * msPerHour);
    for (let uid = 0; uid < 3; uid++) {
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      expect(res.body.dailyLimitExceeded).to.equal(undefined);
      expect(res.body.profiles.length).to.be.gt(0);
    }
  });
  */

  it('shorter_swipe_limit', async () => {

    // Initially, all users should get recommendations
    for (let uid = 0; uid < 3; uid++) {
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

      // Like all the recommendations
      profiles = res.body.profiles;
      for (let i = 0; i < profiles.length; i++) {
        res = await request(app)
          .patch('/v1/user/pass')
          .set('authorization', uid)
          .send({
            user: profiles[i]._id,
          });
        expect(res.status).to.equal(200);
      }

      // After acting on all recommendations, user should get no more
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      console.log(res.body);
      expect(res.status).to.equal(200);
      expect(res.body.dailyLimitExceeded).to.equal(true);

      user = await User.findById(uid.toString());
      expect(user.currentDayMetrics.hitSwipeLimit).to.equal(true);
    }

    // after 12 hours, users should get more recs
    clock.tick(12 * msPerHour);
    for (let uid = 0; uid < 3; uid++) {
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      expect(res.body.dailyLimitExceeded).to.equal(undefined);

      user = await User.findById(uid.toString());
      expect(user.currentDayMetrics.hitSwipeLimit).to.equal(false);
    }
  });

  it('more recommendations for higher karma', async () => {
    user = await User.findOne({ _id: 0 });
    user.karmaTier = 0;
    await user.save();

    user = await User.findOne({ _id: 1 });
    user.karmaTier = 1;
    await user.save();

    // Initially, all users should get recommendations
    for (let uid = 0; uid < 2; uid++) {
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

      // Like all the recommendations
      profiles = res.body.profiles;
      for (let i = 0; i < profiles.length; i++) {
        res = await request(app)
          .patch('/v1/user/pass')
          .set('authorization', uid)
          .send({
            user: profiles[i]._id,
          });
        expect(res.status).to.equal(200);
      }
    }

    // user 0 should get no more
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal(true);

    // user 1 should get 1 more
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal();
    expect(res.body.profiles.length).to.equal(1);
  });

  it('get additional swipes', async () => {
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    // Pass all the recommendations
    profiles = res.body.profiles;
    for (let i = 0; i < profiles.length; i++) {
      res = await request(app)
        .patch('/v1/user/pass')
        .set('authorization', 0)
        .send({
          user: profiles[i]._id,
        });
      expect(res.status).to.equal(200);
    }

    // user 0 should get no more
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal(true);

    // get more swipes
    res = await request(app)
      .put('/v1/user/additionalSwipes')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.not.equal(0);
    expect(res.body.dailyLimitExceeded).to.equal();
  });

  it('numSwipesRemaining', async () => {
    user = await User.findOne({ _id: 0 });
    user.karmaTier = 0;
    await user.save();

    user = await User.findOne({ _id: 1 });
    user.karmaTier = 1;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
    expect(res.body.numSwipesRemaining).to.equal(DAILY_PROFILE_LIMIT);
    profiles = res.body.profiles;

    // numSwipesRemaining in pass/sendLike is approximate
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profiles[0]._id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.numSwipesRemaining).to.equal(DAILY_PROFILE_LIMIT);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: profiles[1]._id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.numSwipesRemaining).to.equal(DAILY_PROFILE_LIMIT - 1);

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profiles[2]._id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.numSwipesRemaining).to.equal(DAILY_PROFILE_LIMIT - 2);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
    expect(res.body.numSwipesRemaining).to.equal(DAILY_PROFILE_LIMIT + 1);
    profiles = res.body.profiles;

    // numSwipesRemaining in pass/sendLike is approximate
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 1)
      .send({
        user: profiles[0]._id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.numSwipesRemaining).to.equal(DAILY_PROFILE_LIMIT + 1);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: profiles[1]._id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.numSwipesRemaining).to.equal(DAILY_PROFILE_LIMIT);

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 1)
      .send({
        user: profiles[2]._id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.numSwipesRemaining).to.equal(DAILY_PROFILE_LIMIT - 1);
  });

  /*
  it('new karma tier swipe limits', async () => {
    sinon.stub(constants, 'getNewKarmaTierSwipeLimits').returns([1, 2, 3]);

    // tier 0 should get 1 profile
    user = await User.findOne({ _id: 0 });
    user.config.new_karma_system = true;
    user.newKarmaTier = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // tier 1 should get 2 profile
    user = await User.findOne({ _id: 0 });
    user.config.new_karma_system = true;
    user.newKarmaTier = 1;
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
  });
  */

  it('unlimited likes subscription', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.unlimitedLikesExpiration).to.equal();

    // purchase unlimited likes
    const receipt = JSON.parse(JSON.stringify(validGoogleReceipt));
    receipt.productId = 'unlimited_likes_1_month';
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 1)
      .send({
        receipt,
      });
    expect(res.status).to.equal(200);

    // reset top picks to avoid affecting test
    // user = await User.findById('1');
    // user.currentDayMetrics.topPicks = [];
    // await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.unlimitedLikesExpiration).to.not.equal();

    // Initially, all users should get recommendations
    for (let uid = 0; uid < 2; uid++) {
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

      // Like all the recommendations
      profiles = res.body.profiles;
      for (let i = 0; i < profiles.length; i++) {
        res = await request(app)
          .patch('/v1/user/pass')
          .set('authorization', uid)
          .send({
            user: profiles[i]._id,
          });
        expect(res.status).to.equal(200);
      }
    }

    // user 0 should get no more
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal(true);

    // user 1 should get 2 more
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal();
    expect(res.body.profiles.length).to.equal(2);
  });
});

function isDisjoint(array1, array2) {
  return new Set([...array1, ...array2]).size == new Set(array1).size + new Set(array2).size;
}

// APP-420
describe('APP-420 dailyProfiles, get all tags with correct order', () => {
  const numUsers = DAILY_PROFILE_LIMIT + 3;

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      let gender = 'female'
      let preference = 'male'
      let personality = 'ESTP'
      let interestNames = ['kpop', 'latin']
      if(uid === 0){
        gender = 'male'
        preference = 'female'
        personality = 'ISTP'
      }

      let createdAt = new Date()
      let longitude = 0
      let latitude = 51
      let actionsReceived = 30

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.13.63' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          gender: [
            'female','male'
          ]
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: gender });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: 1990,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          answers: {},
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          longitude: longitude,
          latitude: latitude,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/interests')
        .set('authorization', uid)
        .send({
          interestNames: interestNames,
        });
      expect(res.status).to.equal(200);

      const user = await User.findOne({ _id: uid });
      user.createdAt = createdAt;
      user.viewableInDailyProfiles = true;
      user.metrics.numActionsReceived = actionsReceived
      user.personality.mbti = personality
      user.scores.likeRatio = 0.7;
      res = await user.save();
    }
    await User.ensureIndexes();
  });


  it('profile with all tags and correct order', async () => {
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasTag = res.body.profiles[0];
    console.log('hasTag :', profileHasTag )
    expect(res.body.profiles[0].tags).to.deep.equal(['Active Now', 'Mutual Interests', 'Nearby', 'Compatible Personality', 'New Soul', 'Top Soul']);
    const metrics = profilesLib.getMetricTags(profileHasTag.tags)

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: profileHasTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    for(let metric of metrics) {
      console.log(`otherUser.metrics.${metric} :`, otherUser.metrics[metric])
      expect(otherUser.metrics[metric]).to.equal(1);
    }
  })
});

describe('APP-420 dailyProfiles, sendLike', () => {
  const numUsers = DAILY_PROFILE_LIMIT + 3;

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
        let gender = 'female'
        let personality = 'ESTJ'
        if (uid === 0){
          gender = 'male'
          preference = 'female'
          personality = 'ISTP'
        }
        let interestNames = ['kpop']

        let createdAt = new Date()
        let longitude = 0
        let latitude = 51
        let totalScore2 = 4
        let actionsReceived = 30
        if(uid !== 0 && uid % 2 == 0){
          interestNames = ['kpop']
          personality = 'ESTP'
          // createdAt = new Date() - (8 * 24 * 60 * 60 * 1000)
          // longitude = 0
          // latitude = 54
        }else if(uid !== 0 && uid % 2 == 1){
          interestNames = ['latin']
          personality = 'ENTP'
          createdAt = new Date() - (8 * 24 * 60 * 60 * 1000)
          longitude = 0
          latitude = 54
          totalScore2 = 2
          actionsReceived = 29
        }


      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.13.63' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          gender: [
            'female','male'
          ]
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: gender });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: 1990,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          answers: {},
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          longitude: longitude,
          latitude: latitude,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/interests')
        .set('authorization', uid)
        .send({
          interestNames: interestNames,
        });
      expect(res.status).to.equal(200);

      const user = await User.findOne({ _id: uid });
      user.createdAt = createdAt;
      user.viewableInDailyProfiles = true;
      user.scores.totalScore2 = totalScore2
      user.personality.mbti = personality
      user.metrics.numActionsReceived = actionsReceived
      if (actionsReceived >= 30) {
        user.scores.likeRatio = 0.7;
      }

      res = await user.save();
    }
    await User.ensureIndexes();
  });


  it('activeNow recommendation tags', async () => {
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasActiveNowTag = res.body.profiles.find(profile => profile.tags.includes('Active Now'));
    console.log('hasTag :', profileHasActiveNowTag )
    expect(profileHasActiveNowTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasActiveNowTag.tags)

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: profileHasActiveNowTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    for(let metric of metrics) {
      console.log(`otherUser.metrics.${metric} :`, otherUser.metrics[metric])
      expect(otherUser.metrics[metric]).to.equal(1);
    }
  })

  it('compatiblePersonality recommendation tags', async () => {
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasCompatiblePersonalityTag = res.body.profiles.find(profile => profile.tags.includes('Compatible Personality'));
    console.log('HasCompatiblePersonalityTag :', profileHasCompatiblePersonalityTag )
    expect(profileHasCompatiblePersonalityTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasCompatiblePersonalityTag.tags)

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: profileHasCompatiblePersonalityTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }

  })

  it('newSoul recommendation tags', async () => {
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasNewSoulTag = res.body.profiles.find(profile => profile.tags.includes('New Soul'));
    console.log('HasCompatiblePersonalityTag :', profileHasNewSoulTag )
    expect(profileHasNewSoulTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasNewSoulTag.tags)

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: profileHasNewSoulTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }

  })

  it('male should see nearby recommendation tag', async () => {
    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' });
    expect(res.status).to.equal(200);

    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasNearbyTag = res.body.profiles.find(profile => profile.tags.includes('Nearby'));
    console.log('HasCompatiblePersonalityTag :', profileHasNearbyTag )
    expect(profileHasNearbyTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasNearbyTag.tags)

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: profileHasNearbyTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }
  })

  it('female should not see nearby recommendation tag', async () => {
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 1);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasNearbyTag = res.body.profiles.find(profile => profile.tags.includes('Nearby'));
    console.log('HasCompatiblePersonalityTag :', profileHasNearbyTag )
    expect(profileHasNearbyTag).to.be.undefined;

  })

  it('topSoul recommendation tags', async () => {
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasTopSoulTag = res.body.profiles.find(profile => profile.tags.includes('Top Soul'));
    console.log('HasCompatiblePersonalityTag :', profileHasTopSoulTag )
    expect(profileHasTopSoulTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasTopSoulTag.tags)

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: profileHasTopSoulTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }

  })

  it('interest recommendation tags', async () => {
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasMutualInterestsTag = res.body.profiles.find(profile => profile.tags.includes('Mutual Interests'));
    console.log('HasCompatiblePersonalityTag :', profileHasMutualInterestsTag )
    expect(profileHasMutualInterestsTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasMutualInterestsTag.tags)

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: profileHasMutualInterestsTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }

  })


});

describe('APP-420 dailyProfiles, pass & rewind', () => {
  const numUsers = DAILY_PROFILE_LIMIT + 3;

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      let interestNames = ['kpop']
      let personality = 'ESTJ'
      let gender = 'female'
      if (uid === 0){
        gender = 'male'
        personality = 'ISTP'
      }
      let createdAt = new Date()
      let longitude = 0
      let latitude = 51
      let actionsReceived = 30
      if(uid !== 0 && uid % 2 == 0){
        interestNames = ['kpop']
        personality = 'ESTP'
        createdAt = new Date() - (8 * 24 * 60 * 60 * 1000)
        longitude = 0
        latitude = 54
      }else if(uid === 1){
        interestNames = ['latin']
        personality = 'ENTP'
        createdAt = new Date() - (8 * 24 * 60 * 60 * 1000)
        longitude = 0
        latitude = 54
        actionsReceived = 29
      }

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.13.63' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          gender: [
            'female','male'
          ]
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: gender });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: 1990,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          answers: {},
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          longitude: longitude,
          latitude: latitude,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/interests')
        .set('authorization', uid)
        .send({
          interestNames: interestNames,
        });
      expect(res.status).to.equal(200);

      const user = await User.findOne({ _id: uid });
      user.createdAt = createdAt;
      user.viewableInDailyProfiles = true;
      user.personality.mbti = personality
      user.metrics.numActionsReceived = actionsReceived
      res = await user.save();
    }
    await User.ensureIndexes();
  });


  it('pass & rewind, activeNow recommendation tags', async () => {
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasActiveNowTag = res.body.profiles.find(profile => profile.tags.includes('Active Now'));
    console.log('hasTag :', profileHasActiveNowTag )
    expect(profileHasActiveNowTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasActiveNowTag.tags)

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profileHasActiveNowTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    for(let metric of metrics) {
      console.log(`otherUser.metrics.${metric} :`, otherUser.metrics[metric])
      expect(otherUser.metrics[metric]).to.equal(1);
    }

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();

    delete profileHasActiveNowTag.stories;
    // console.log('profileHasActiveNowTag: ', profileHasActiveNowTag)

    res = await request(app)
      .get('/v1/user/rewind')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user).to.eql(profileHasActiveNowTag);
    expect(res.body.user.tags).to.eql(profileHasActiveNowTag.tags);

  })

  it('pass & rewind, compatiblePersonality recommendation tags', async () => {
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasCompatiblePersonalityTag = res.body.profiles.find(profile => profile.tags.includes('Compatible Personality'));
    console.log('HasCompatiblePersonalityTag :', profileHasCompatiblePersonalityTag )
    expect(profileHasCompatiblePersonalityTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasCompatiblePersonalityTag.tags)

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profileHasCompatiblePersonalityTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();

    delete profileHasCompatiblePersonalityTag.stories;
    // console.log('profileHasActiveNowTag: ', profileHasActiveNowTag)

    res = await request(app)
      .get('/v1/user/rewind')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user).to.eql(profileHasCompatiblePersonalityTag);
    expect(res.body.user.tags).to.eql(profileHasCompatiblePersonalityTag.tags);

  })

  it('pass & rewind, newSoul recommendation tags', async () => {
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasNewSoulTag = res.body.profiles.find(profile => profile.tags.includes('New Soul'));
    console.log('HasCompatiblePersonalityTag :', profileHasNewSoulTag )
    expect(profileHasNewSoulTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasNewSoulTag.tags)

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profileHasNewSoulTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();

    delete profileHasNewSoulTag.stories;
    // console.log('profileHasActiveNowTag: ', profileHasActiveNowTag)

    res = await request(app)
      .get('/v1/user/rewind')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user).to.eql(profileHasNewSoulTag);
    expect(res.body.user.tags).to.eql(profileHasNewSoulTag.tags);

  })

  it('pass & rewind, male should see nearby recommendation tag', async () => {
    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' });
    expect(res.status).to.equal(200);

    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasNearbyTag = res.body.profiles.find(profile => profile.tags.includes('Nearby'));
    console.log('HasCompatiblePersonalityTag :', profileHasNearbyTag )
    expect(profileHasNearbyTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasNearbyTag.tags)

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profileHasNearbyTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();

    delete profileHasNearbyTag.stories;
    // console.log('profileHasNearbyTag: ', profileHasActiveNowTag)

    res = await request(app)
      .get('/v1/user/rewind')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user).to.eql(profileHasNearbyTag);
    expect(res.body.user.tags).to.eql(profileHasNearbyTag.tags);
  })

  it('pass & rewind, topSoul recommendation tags', async () => {
    let user = await User.findOne({ _id: 3 });
    user.scores.likeRatio = 0.9;
    user.scores.numActionsReceived = 10;
    user.metrics.numLikesReceived = 9;
    await user.save();

    let res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    const profileHasTopSoulTag = res.body.profiles.find(profile => profile.tags.includes('Top Soul'));
    expect(profileHasTopSoulTag).to.not.equal(undefined);
    const metrics = profilesLib.getMetricTags(profileHasTopSoulTag.tags);

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profileHasTopSoulTag._id,
      });
    expect(res.status).to.equal(200);

    let otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for (let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();

    delete profileHasTopSoulTag.stories;
    // console.log('profileHasTopSoulTag: ', profileHasActiveNowTag)

    user = await User.findOne({ _id: 3 });
    console.log('user.scores.likeRatio :', user.scores.likeRatio);

    res = await request(app)
      .get('/v1/user/rewind')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user).to.eql(profileHasTopSoulTag);
    expect(res.body.user.tags).to.eql(profileHasTopSoulTag.tags);
  });

  it('pass & rewind, interest recommendation tags', async () => {
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasMutualInterestsTag = res.body.profiles.find(profile => profile.tags.includes('Mutual Interests'));
    console.log('HasCompatiblePersonalityTag :', profileHasMutualInterestsTag )
    expect(profileHasMutualInterestsTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasMutualInterestsTag.tags)

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profileHasMutualInterestsTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();

    delete profileHasMutualInterestsTag.stories;
    // console.log('profileHasMutualInterestsTag: ', profileHasActiveNowTag)

    res = await request(app)
      .get('/v1/user/rewind')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user).to.eql(profileHasMutualInterestsTag);
    expect(res.body.user.tags).to.eql(profileHasMutualInterestsTag.tags);

  })
})

describe('APP-420 no profile tags', () => {
  const numUsers = DAILY_PROFILE_LIMIT + 3;

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
        let gender = 'female'
        let personality = 'ESTJ'
        let longitude = 0
        let latitude = 51
        let interestNames = ['kpop']
        if (uid === 0){
          interestNames = ['latin']
          gender = 'male'
          preference = 'female'
          personality = 'INTP'
          latitude = 52
        }


        let createdAt = new Date() - (8 * 24 * 60 * 60 * 1000)

        let totalScore2 = 2
        // if(uid !== 0 && uid % 2 == 0){
        //   interestNames = ['kpop']
        //   personality = 'ESTP'
        //   // createdAt = new Date() - (8 * 24 * 60 * 60 * 1000)
        //   // longitude = 0
        //   // latitude = 54
        // }else if(uid !== 0 && uid % 2 == 1){
        //   interestNames = ['latin']
        //   personality = 'ENTP'
        //   createdAt = new Date() - (8 * 24 * 60 * 60 * 1000)
        //   longitude = 0
        //   latitude = 54
        //   totalScore2 = 2
        // }


      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.13.63' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          gender: [
            'female','male'
          ]
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: gender });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: 1990,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          answers: {},
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          longitude: longitude,
          latitude: latitude,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/interests')
        .set('authorization', uid)
        .send({
          interestNames: interestNames,
        });
      expect(res.status).to.equal(200);

      const user = await User.findOne({ _id: uid });
      user.createdAt = createdAt;
      user.viewableInDailyProfiles = true;
      user.scores.totalScore2 = totalScore2
      user.personality.mbti = personality
      res = await user.save();
    }
    await User.ensureIndexes();
  });

  it('no profile tags', async () => {
    userNoTags = await User.findOne({ _id: 1 });
    userNoTags.metrics.lastSeen = new Date() - (35 * 60 * 1000)
    await userNoTags.save()

    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    console.log('res.body.profiles :', res.body.profiles)

    const profileHasNoInterestsTag = res.body.profiles.find(profile => profile.tags.length === 0);
    console.log('HasNoTag :', profileHasNoInterestsTag )
    expect(profileHasNoInterestsTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasNoInterestsTag.tags)
    console.log('metrics :', metrics)

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: profileHasNoInterestsTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }

  })

  it('pass, no profile tags', async () => {
    userNoTags = await User.findOne({ _id: 1 });
    userNoTags.metrics.lastSeen = new Date() - (35 * 60 * 1000)
    await userNoTags.save()

    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    console.log('res.body.profiles :', res.body.profiles)

    const profileHasNoInterestsTag = res.body.profiles.find(profile => profile.tags.length === 0);
    console.log('HasNoTag :', profileHasNoInterestsTag )
    expect(profileHasNoInterestsTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasNoInterestsTag.tags)

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profileHasNoInterestsTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }

  })

})

describe('APP-420 top picks', () => {
  const numUsers = 9;

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      let personality = 'ISTP'
      let interestNames = ['kpop']
      let gender = 'female'
      if (uid === 0){
        gender = 'male'
        personality = 'ISTP'
      }

      let createdAt = new Date()
      let longitude = 0
      let latitude = 51
      let actionsReceived = 29
      if(uid !== 0 && uid % 2 == 0){
        interestNames = ['kpop']
        personality = 'ESTP'
      //   createdAt = new Date() - (8 * 24 * 60 * 60 * 1000)
        longitude = 0
        latitude = 51.0095
        actionsReceived = 30
        if(uid === 2) latitude = 51
      }else if(uid === 1 ){
        interestNames = ['latin']
        personality = 'ENTP'
        createdAt = new Date() - (8 * 24 * 60 * 60 * 1000)
        longitude = 0
        latitude = 51.0095
        actionsReceived = 20

      }

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.13.63' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          gender: [
            'female', 'male'
          ]
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: gender });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: 1990,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          answers: {},
        });
      expect(res.status).to.equal(200);
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          longitude: longitude,
          latitude: latitude,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/interests')
        .set('authorization', uid)
        .send({
          interestNames: interestNames,
        });
      expect(res.status).to.equal(200);

      const user = await User.findOne({ _id: uid });
      user.createdAt = createdAt;
      user.viewableInDailyProfiles = true;
      user.metrics.numActionsReceived = actionsReceived
      user.personality.mbti = personality

      if (actionsReceived >= 30) {
        user.scores.likeRatio = 0.7;
      }

      res = await user.save();
    }
    await User.ensureIndexes();
  });

  afterEach(() => {
  });

  it('activeNow, send like topPicks', async () => {
    user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 5;
    await user.save();

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    expect(res.body.alreadySuperLiked).to.eql([]);

    const profileHasTag = res.body.profiles.find(profile => profile.tags.includes('Active Now'));
    // console.log('hasTag :', profileHasTag )
    expect(profileHasTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasTag.tags)

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: profileHasTag._id,
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });

    for(let metric of metrics) {
      console.log(`otherUser.metrics.${metric} :`, otherUser.metrics[metric])
      expect(otherUser.metrics[metric]).to.equal(1);
    }
  });

  it('compatiblePersonality, send like topPicks', async () => {
    user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 5;
    await user.save();

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    expect(res.body.alreadySuperLiked).to.eql([]);

    const profileHasTag = res.body.profiles.find(profile => profile.tags.includes('Compatible Personality'));
    // console.log('hasTag :', profileHasTag )
    expect(profileHasTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasTag.tags)

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: profileHasTag._id,
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }
  });

  it('male, should see nearby tags, send like topPicks', async () => {
    user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 5;
    await user.save();

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    expect(res.body.alreadySuperLiked).to.eql([]);

    console.log('res.body.profiles :', res.body.profiles)

    const profileHasTag = res.body.profiles.find(profile => profile.tags.includes('Nearby'));
    // console.log('hasTag :', profileHasTag )
    expect(profileHasTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasTag.tags)

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: profileHasTag._id,
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }
  });

  it('female, should not see nearby tags, send like topPicks', async () => {
    user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 5;
    await user.save();

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    expect(res.body.alreadySuperLiked).to.eql([]);

    console.log('res.body.profiles :', res.body.profiles)

    const profileHasTag = res.body.profiles.find(profile => profile.tags.includes('Nearby'));
    // console.log('hasTag :', profileHasTag )
    expect(profileHasTag).to.be.undefined;
  });

  it('newSoul, send like topPicks', async () => {
    user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 5;
    await user.save();

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    expect(res.body.alreadySuperLiked).to.eql([]);

    const profileHasTag = res.body.profiles.find(profile => profile.tags.includes('New Soul'));
    // console.log('hasTag :', profileHasTag )
    expect(profileHasTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasTag.tags)

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: profileHasTag._id,
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }
  });

  it('topSoul, send like topPicks', async () => {
    user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 5;
    await user.save();

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    console.log('profile :', res.body.profiles)
    expect(res.body.alreadySuperLiked).to.eql([]);

    const profileHasTag = res.body.profiles.find(profile => profile.tags.includes('Top Soul'));
    console.log('hasTag :', profileHasTag )
    //expect(profileHasTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasTag.tags)

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: profileHasTag._id,
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }
  });

  it('interest, send like topPicks', async () => {
    user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 5;
    await user.save();

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    expect(res.body.alreadySuperLiked).to.eql([]);

    const profileHasTag = res.body.profiles.find(profile => profile.tags.includes('Mutual Interests'));
    // console.log('hasTag :', profileHasTag )
    expect(profileHasTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasTag.tags)

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: profileHasTag._id,
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    // console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }
  });

  it('no profile tags, send like topPicks', async () => {
    user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 5;
    await user.save();

    userNoTags = await User.findOne({ _id: 1 });
    userNoTags.metrics.lastSeen = new Date() - (35 * 60 * 1000)
    await userNoTags.save()

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    expect(res.body.alreadySuperLiked).to.eql([]);

    console.log('res.body.profiles :', res.body.profiles)

    const profileHasNoInterestsTag = res.body.profiles.find(profile => profile.tags.length === 0);
    console.log('HasNoTag :', profileHasNoInterestsTag )
    expect(profileHasNoInterestsTag).to.be.not.undefined;
    const metrics = profilesLib.getMetricTags(profileHasNoInterestsTag.tags)

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: profileHasNoInterestsTag._id,
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    console.log('otherUser.metrics :', otherUser.metrics)
    for(let metric of metrics) {
      expect(otherUser.metrics[metric]).to.equal(1);
    }

  })

})

describe('APP-506 topSoul Experiment', () => {
  const numUsers = DAILY_PROFILE_LIMIT + 3;

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      let gender = 'female'
      let preference = 'male'
      let personality = 'ESTP'
      let interestNames = ['kpop', 'latin']
      if(uid === 0){
        gender = 'male'
        preference = 'female'
        personality = 'ISTP'
      }

      let createdAt = new Date()
      let longitude = 0
      let latitude = 51
      let totalScore2 = 4

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.13.69' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          gender: [
            'female','male'
          ]
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: gender });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: 1990,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          answers: {},
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          longitude: longitude,
          latitude: latitude,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/interests')
        .set('authorization', uid)
        .send({
          interestNames: interestNames,
        });
      expect(res.status).to.equal(200);

      const user = await User.findOne({ _id: uid });
      user.createdAt = createdAt;
      user.viewableInDailyProfiles = true;
      user.personality.mbti = personality
      res = await user.save();
    }
    await User.ensureIndexes();
  });

  it('APP-506 true likeRatio >= 18 & numActionsReceived >= 30', async () => {
    let actionsReceived = 30
    let totalScore2 = 2
    for (let uid = 0; uid < numUsers; uid++) {
      const user = await User.findOne({ _id: uid });
      user.scores.totalScore2 = totalScore2
      user.metrics.numActionsReceived = actionsReceived
      user.scores.likeRatio = 0.70;
      res = await user.save();
    }
    await User.ensureIndexes();

    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

    const profileHasTag = res.body.profiles.find(profile => profile.tags.includes('Top Soul'));
    console.log('hasTag :', profileHasTag )
    expect(res.body.profiles[0].tags).to.deep.equal(['Active Now', 'Mutual Interests', 'Nearby', 'Compatible Personality', 'New Soul', 'Top Soul']);
    const metrics = profilesLib.getMetricTags(profileHasTag.tags)

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: profileHasTag._id,
      });
    expect(res.status).to.equal(200);

    otherUser = await User.findOne({ _id: 0 });
    for(let metric of metrics) {
      console.log(`otherUser.metrics.${metric} :`, otherUser.metrics[metric])
      expect(otherUser.metrics[metric]).to.equal(1);
    }
  });

  it('APP-506 true likeRatio >= 18 & numActionsReceived = 29, no profiles has topSoul tags', async () => {
    let actionsReceived = 29
    let totalScore2 = 4
    for (let uid = 0; uid < numUsers; uid++) {
      const user = await User.findOne({ _id: uid });
      user.scores.totalScore2 = totalScore2
      user.metrics.numActionsReceived = actionsReceived
      res = await user.save();
    }
    await User.ensureIndexes();

    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);

    console.log('profiles :', res.body.profiles)
    const profileHasTag = res.body.profiles.find(profile => profile.tags.includes('Top Soul'));
    expect(profileHasTag).to.equal(undefined)
  });

})

describe('dailyProfiles exclude profileIds', () => {
  const numUsers = DAILY_PROFILE_LIMIT + 3;

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      let personality = 'ISTP'
      let interestNames = ['kpop', 'latin']
      if(uid !== 0){
        personality = 'ESTP'
      }

      let createdAt = new Date()
      let longitude = 0
      let latitude = 51
      let totalScore2 = 4

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          gender: [
            'female',
          ]
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: 1990,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          answers: {},
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          longitude: longitude,
          latitude: latitude,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/interests')
        .set('authorization', uid)
        .send({
          interestNames: interestNames,
        });
      expect(res.status).to.equal(200);

      const user = await User.findOne({ _id: uid });
      user.createdAt = createdAt;
      user.viewableInDailyProfiles = true;
      user.scores.totalScore2 = totalScore2
      user.personality.mbti = personality
      res = await user.save();
    }
    await User.ensureIndexes();
  })

  it('no profileIds exluded, profileId not found', async () => {

    const excludeProfileIds = '7'

    //first hit without any exclude profile
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
    res.body.profiles.forEach(profile => {
      expect(['0','2','3','4']).to.include(profile._id);
    });

    // pending swipe counts toward limit
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 1)
        .query({excludeProfileIds})
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT - 1);

    res.body.profiles.forEach(profile => {
      expect(['0','2','3','4']).to.include(profile._id);
    });

  })


  it('success exclude profileIds', async () => {

    const excludeProfileIds = ['1', '2']

    //first hit without any exclude profile
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
    res.body.profiles.forEach(profile => {
      expect(['1','2','3','4']).to.include(profile._id);
    });

    //second hit, exclude profile 1,2
    res = await request(app)
        .get(`/v1/user/dailyProfiles`)
        .set('authorization', 0)
        .query({excludeProfileIds})
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);

    res.body.profiles.forEach(profile => {
      expect(excludeProfileIds).to.not.include(profile._id);
    });

  })

  it('success exclude one profileId', async () => {

    const excludeProfileIds = ['1']

    //first hit without any exclude profile
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
    res.body.profiles.forEach(profile => {
      expect(['0','1','3','4']).to.include(profile._id);
    });

    //second hit, exclude profile 1,2
    res = await request(app)
        .get(`/v1/user/dailyProfiles`)
        .set('authorization', 2)
        .query({excludeProfileIds})
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(3);

    res.body.profiles.forEach(profile => {
      expect(excludeProfileIds).to.not.include(profile._id);
    });

  })

  it('count pending swipes toward swiping limit', async () => {

    // set limit to 2
    constants.getKarmaTierSwipeLimits.restore();
    sinon.stub(constants, 'getKarmaTierSwipeLimits').returns([2]);

    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body.profiles);
    expect(res.body.profiles.length).to.equal(2);

    // send a swipe
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body.profiles);
    expect(res.body.profiles.length).to.equal(1);

    // get more profiles, with one pending swipe which should count toward limit
    res = await request(app)
        .get(`/v1/user/dailyProfiles`)
        .set('authorization', 0)
        .query({excludeProfileIds: ['2']})
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal(true);
    expect(res.body.dailyLimitResetsAt).to.not.equal();

    // next day
    user = await User.findById('0');
    user.metrics.swipeLimitResetTime = Date.now();
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
        .get(`/v1/user/dailyProfiles`)
        .set('authorization', 0)
    expect(res.status).to.equal(200);
    console.log(res.body.profiles);
    expect(res.body.profiles.length).to.equal(2);
  })

  it('remove dm from pending swipes', async () => {

    // set limit to 2
    constants.getKarmaTierSwipeLimits.restore();
    sinon.stub(constants, 'getKarmaTierSwipeLimits').returns([2]);

    // get profiles, with one pending swipe which should count toward limit
    res = await request(app)
        .get(`/v1/user/dailyProfiles`)
        .set('authorization', 0)
        .query({excludeProfileIds: ['1']})
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // complete the dm
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'Hi',
        price: 50,
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    // should get two profiles because dm doesn't count toward swipe limit
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body.profiles);
    expect(res.body.profiles.length).to.equal(2);
  })

  it('remove super love from pending swipes', async () => {

    user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 1;
    await user.save();

    // set limit to 2
    constants.getKarmaTierSwipeLimits.restore();
    sinon.stub(constants, 'getKarmaTierSwipeLimits').returns([2]);

    // get profiles, with one pending swipe which should count toward limit
    res = await request(app)
        .get(`/v1/user/dailyProfiles`)
        .set('authorization', 0)
        .query({excludeProfileIds: ['1']})
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // complete the super love
    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    // should get two profiles because super love doesn't count toward swipe limit
    res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body.profiles);
    expect(res.body.profiles.length).to.equal(2);
  })

  it('if already in swipes, dmSuperLiked should not be in pendingSwipes', async () => {

    let user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 1;
    await user.save();

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '2',
        message: 'Hi',
        price: 50,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    //send super love
    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: '3',
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    expect(user.currentDayMetrics.swipes.length).to.equal(1);
    expect(user.currentDayMetrics.swipes).to.include('1');
    expect(user.currentDayMetrics.dmSuperLikes.length).to.equal(2);
    expect(user.currentDayMetrics.dmSuperLikes).to.include('2');
    expect(user.currentDayMetrics.dmSuperLikes).to.include('3');

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .query({excludeProfileIds: ['1','2','3','4']})
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    expect(user.currentDayMetrics.swipes.length).to.equal(1);
    expect(user.currentDayMetrics.swipes).to.include('1');
    expect(user.currentDayMetrics.dmSuperLikes.length).to.equal(2);
    expect(user.currentDayMetrics.dmSuperLikes).to.include('2');
    expect(user.currentDayMetrics.dmSuperLikes).to.include('3');
    expect(user.currentDayMetrics.pendingSwipes.length).to.equal(1);
    expect(user.currentDayMetrics.pendingSwipes).to.include('4');
  })

})

describe('rewind', () => {
  const numUsers = 3;

  const userProfile0 = {
    _id: '0',
    age: 31,
    crown: false,
    hideQuestions: false,
    hideComments: false,
    description: '',
    location: 'Honolulu, HI 🇺🇸',
    nearby: true,
    teleport: false,
    education: '',
    work: 'work',
    enneagram: '1w9',
    prompts: [{
      id: promptsLib.promptsArray[0].id,
      prompt: promptsLib.promptsArray[0].prompt,
      answer: '0',
    }],
    interests: [],
    interestNames: [],
    firstName: '',
    gender: 'female',
    handle: 'handle0',
    personality: { mbti: 'ESTJ', avatar: 'Executive' },
    relationshipStatus: "Single",
    datingSubPreferences: "Short term fun",
    relationshipType: "Polyamorous",
    pictures: ['picture0', 'picture1'],
    profilePicture: 'picture0',
    preferences: { purpose: ['friends'] },
    horoscope: 'Capricorn',
    karma: 0,
    numFollowers: 0,
    verified: false,
    verificationStatus: 'unverified',
    stories: [],
  };
  const userProfile1 = {
    _id: '1',
    age: 31,
    crown: false,
    hideQuestions: false,
    hideComments: false,
    description: '',
    location: 'Kailua, HI 🇺🇸',
    nearby: true,
    teleport: true,
    education: '',
    work: 'work',
    prompts: [{
      id: promptsLib.promptsArray[0].id,
      prompt: promptsLib.promptsArray[0].prompt,
      answer: '1',
    }],
    interests: [],
    interestNames: [],
    firstName: '',
    gender: 'female',
    handle: 'handle1',
    personality: { mbti: 'ESTJ', avatar: 'Executive' },
    relationshipStatus: "Single",
    datingSubPreferences: "Short term fun",
    relationshipType: "Polyamorous",
    "tags": [
        "Active Now",
        "New Soul",
    ],
    pictures: ['picture0', 'picture1'],
    profilePicture: 'picture0',
    preferences: { purpose: ['friends'] },
    horoscope: 'Capricorn',
    karma: 0,
    numFollowers: 0,
    verified: false,
    verificationStatus: 'unverified',
    stories: [],
  };
  const userProfile2 = {
    _id: '2',
    age: 31,
    crown: false,
    hideQuestions: false,
    hideComments: false,
    description: '',
    location: 'Honolulu, HI 🇺🇸',
    nearby: true,
    teleport: false,
    education: '',
    work: 'work',
    prompts: [{
      id: promptsLib.promptsArray[0].id,
      prompt: promptsLib.promptsArray[0].prompt,
      answer: '2',
    }],
    interests: [],
    interestNames: [],
    firstName: '',
    gender: 'female',
    handle: 'handle2',
    personality: { mbti: 'ESTJ', avatar: 'Executive' },
    relationshipStatus: "Single",
    "tags": [
        "Active Now",
        "New Soul",
    ],
    datingSubPreferences: "Short term fun",
    relationshipType: "Polyamorous",
    pictures: ['picture0', 'picture1'],
    profilePicture: 'picture0',
    preferences: { purpose: ['friends'] },
    horoscope: 'Capricorn',
    karma: 0,
    numFollowers: 0,
    verified: false,
    verificationStatus: 'unverified',
    stories: [],
  };

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      await initUser(uid);
    }

    // teleport for user 1
    user = await User.findOne({ _id: 1 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .put('/v1/teleport/location')
      .set('authorization', 1)
      .send({
        latitude: 21.40,
        longitude: -157.74,
      });
    expect(res.status).to.equal(200);

    // enneagram for user 0
    res = await request(app)
      .put('/v1/user/enneagram')
      .set('authorization', 0)
      .send({ enneagram: '1w9' });
    expect(res.status).to.equal(200);
  });

  it('rewind', async () => {
    // set coins to 0
    userMetadata = await UserMetadata.findOne({ user: 0 });
    userMetadata.coins = 0;
    userMetadata = await userMetadata.save();

    // User 0 passes user 1
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: '1',
      });

    // User 0 tries rewind - doesn't work because not premium
    res = await request(app)
      .get('/v1/user/rewind')
      .set('authorization', 0);
    expect(res.status).to.equal(403);
    expect(res.body.user).to.equal(undefined);

    user = await User.findById('0');
    expect(user.metrics.numRewindUsed).to.equal(0);

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();

    // Rewind should work now
    delete userProfile1.stories;
    res = await request(app)
      .get('/v1/user/rewind')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user).to.eql(userProfile1);
    expect(res.body.coinsRemaining).to.eql(0);

    user = await User.findById('0');
    expect(user.metrics.numRewindUsed).to.equal(1);
  });

  it('rewind with coins', async () => {
    // set coins to 0
    userMetadata = await UserMetadata.findOne({ user: 0 });
    userMetadata.coins = 0;
    userMetadata = await userMetadata.save();

    // incorrect price
    res = await request(app)
      .get('/v1/user/rewind')
      .query({ price: coinsConstants.rewindCost + 1 })
      .set('authorization', 0);
    expect(res.status).to.equal(409);

    // insufficient coins
    res = await request(app)
      .get('/v1/user/rewind')
      .query({ price: coinsConstants.rewindCost })
      .set('authorization', 0);
    expect(res.status).to.equal(403);

    user = await User.findById('0');
    expect(user.metrics.numRewindUsed).to.equal(0);

    // grant some coins
    userMetadata = await UserMetadata.findOne({ user: 0 });
    userMetadata.coins = coinsConstants.rewindCost + 10;
    userMetadata = await userMetadata.save();

    // User 0 passes user 1
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: '1',
      });

    // Rewind should work now
    delete userProfile1.stories;
    res = await request(app)
      .get('/v1/user/rewind')
      .query({ price: coinsConstants.rewindCost })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user).to.eql(userProfile1);
    expect(res.body.coinsRemaining).to.eql(10);

    user = await User.findById('0');
    expect(user.metrics.numRewindUsed).to.equal(1);

    // Not enough coins to use again
    res = await request(app)
      .get('/v1/user/rewind')
      .query({ price: coinsConstants.rewindCost })
      .set('authorization', 0);
    expect(res.status).to.equal(403);

    // grant some coins
    userMetadata = await UserMetadata.findOne({ user: 0 });
    userMetadata.coins = coinsConstants.rewindCost + 10;
    userMetadata = await userMetadata.save();

    // Rewind again should return same user, but not deduct coins
    res = await request(app)
      .get('/v1/user/rewind')
      .query({ price: coinsConstants.rewindCost })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user).to.eql(userProfile1);
    expect(res.body.coinsRemaining).to.eql(coinsConstants.rewindCost + 10);

    user = await User.findById('0');
    expect(user.metrics.numRewindUsed).to.equal(1);
  });

  it('rewind with profileId', async () => {
    // set coins to 0
    userMetadata = await UserMetadata.findOne({ user: 0 });
    userMetadata.coins = 0;
    userMetadata = await userMetadata.save();

    // User 0 passes user 1
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: '1',
      });

    // User 0 passes user 2
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: '2',
      });

    // User 0 tries rewind - doesn't work because not premium
    res = await request(app)
      .get('/v1/user/rewind')
      .set('authorization', 0)
      .query({
        profileId: '1',
      })
    expect(res.status).to.equal(403);
    expect(res.body.user).to.equal(undefined);

    user = await User.findById('0');
    expect(user.metrics.numRewindUsed).to.equal(0);

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();

    // Rewind without specific profile, should get userProfile2
    delete userProfile2.stories;
    res = await request(app)
      .get('/v1/user/rewind')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user).to.eql(userProfile2);
    expect(res.body.coinsRemaining).to.eql(0);

    // Rewind with specific profile, should get userProfile1
    delete userProfile1.stories;
    res = await request(app)
      .get('/v1/user/rewind')
      .set('authorization', 0)
      .query({
        profileId: '1',
      })
    expect(res.status).to.equal(200);
    expect(res.body.user).to.eql(userProfile1);
    expect(res.body.coinsRemaining).to.eql(0);

    user = await User.findById('0');
    expect(user.metrics.numRewindUsed).to.equal(2);
  });

})


describe('top picks', () => {
  const numUsers = 11;
  let clock;

  // January 1, 2017 12:00:00 AM UTC
  initDateMs = 1483228800000;

  beforeEach(async () => {
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          gender: [
            'female',
          ],
          personality: [
            'ISTP',
          ],
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: 1990,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          answers: {},
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          longitude: 0,
          latitude: 51,
        });
      expect(res.status).to.equal(200);

      const user = await User.findOne({ _id: uid });
      user.createdAt = 0;
      user.viewableInDailyProfiles = true;
      res = await user.save();
    }
    await User.ensureIndexes();
  });

  afterEach(() => {
    clock.restore();
  });

  it('reset time', async () => {

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    profileIds = res.body.profiles.map(x => x._id);

    // calling top picks again should return the same profiles
    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(profileIds);

    // after 25 hours, user should receive new top picks
    clock.tick(25 * msPerHour);

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(isDisjoint(res.body.profiles.map(x => x._id), profileIds));
  });

  it('current top picks should be excluded from daily profiles', async () => {

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    profileIds = res.body.profiles.map(x => x._id);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(isDisjoint(res.body.profiles.map(x => x._id), profileIds));
  });

  it('recent daily profiles should be excluded from top picks', async () => {
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(4);
    profileIds = res.body.profiles.map(x => x._id);

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(6);
    expect(isDisjoint(res.body.profiles.map(x => x._id), profileIds));
  });

  it('get more top picks for premium users', async () => {
    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
  });

  it('send super like to top pick', async () => {
    user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 5;
    await user.save();

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    expect(res.body.alreadySuperLiked).to.eql([]);
    profileIds = res.body.profiles.map(x => x._id);

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: profileIds[0],
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(profileIds);
    expect(res.body.alreadySuperLiked).to.eql([profileIds[0]]);

    clock.tick(25 * msPerHour);

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.alreadySuperLiked).to.eql([]);
  });

  it('remove blocked and banned users from top picks', async () => {

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    profileIds = res.body.profiles.map(x => x._id);

    idToBlock = profileIds[0];
    idToBan = profileIds[1];

    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 0)
      .send({ user: idToBlock });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(profileIds.length - 1);
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(profileIds.slice(1));

    await User.findOneAndUpdate(
      { _id: idToBan },
      { shadowBanned: true },
    );

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(profileIds.length - 2);
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(profileIds.slice(2));

    // all empty slots should be filled after purchasing
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(profileIds.length - 2); //banned and blocked shouldnt appear in toppicks
    profileIds = res.body.profiles.map(x => x._id);

    idToBlock = profileIds[0];
    idToBan = profileIds[1];

    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 0)
      .send({ user: idToBlock });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(profileIds.length - 1);
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(profileIds.slice(1));

    await User.findOneAndUpdate(
      { _id: idToBan },
      { shadowBanned: true },
    );

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(profileIds.length - 2);
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(profileIds.slice(2));
  });

  it('remove not-visible users from top picks', async () => {

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(8);
    profileIds = res.body.profiles.map(x => x._id);

    idToHide = profileIds[0];

    res = await request(app)
      .put('/v1/user/hidden')
      .set('authorization', idToHide)
      .send({
        hidden: true,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/topPicks')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(profileIds.length - 1);
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(profileIds.slice(1));
  });
});

describe('location', () => {
  const numUsers = 2;

  async function createUser(uid) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid);
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        gender: [
          'female',
        ],
        personality: [
          'ISTP',
        ],
      });
    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: 'female' });
    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1,
      });
    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {},
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
  }

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      await createUser(uid);
    }
    await User.ensureIndexes();
  });

  it('spirit realm', async () => {
    // both users in spirit realm - no results
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.location).to.equal(null);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.location).to.equal(null);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    expect(res.body.noUsersNearby).to.equal(true);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    expect(res.body.noUsersNearby).to.equal(true);

    // User 0: Honolulu
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    expect(res.body.noUsersNearby).to.equal(true);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].location).to.equal('Hawaii, United States 🇺🇸');

    // send like
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // view the spirit realm user
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].user._id).to.equal('1');
    expect(res.body[0].user.location).to.equal(null);
  });

  it('users in the same country', async () => {
    // User 0: Honolulu
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    // User 1: Brooklyn
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 40.63,
        longitude: -73.95,
      });
    expect(res.status).to.equal(200);

    // location should be returned
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].location).to.equal('Flatlands, NY 🇺🇸');

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].location).to.equal('Honolulu, HI 🇺🇸');
  });

  /*
  it('user with pending report', async () => {
    // same location
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    // user 0 reports user 1
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    // user 1 hidden from daily boos
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // dismiss report
    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { all: true };
    res = await user.save();

    res = await request(app)
      .put('/v1/admin/dismissReports')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    user.admin = false;
    user.adminPermissions = undefined;
    res = await user.save();

    // user 1 not hidden from daily boos
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
  });
  */

  it('user in banned country', async () => {
    // User 0: Honolulu
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    // User 1: Ghana - automatic ban
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 0,
        longitude: 0,
      });
    expect(res.status).to.equal(200);

    // banned user should not be shown in recommendations
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
  });

  it('various locations - US user', async () => {
    // User 0: Honolulu
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    const testdata = [
      // [latitude, longitude, location, location without city]
      [21.30, -157.85, 'Honolulu, HI 🇺🇸', 'Hawaii 🇺🇸'],
      [21.40, -157.74, 'Kailua, HI 🇺🇸', 'Hawaii 🇺🇸'],
      [40.73, -74.00, 'East Village, NY 🇺🇸', 'New York 🇺🇸'],
      [40.63, -73.95, 'Flatlands, NY 🇺🇸', 'New York 🇺🇸'],
      [41.31, -72.92, 'New Haven, CT 🇺🇸', 'Connecticut 🇺🇸'],
      [19.35, -99.15, 'Mexico City, Mexico 🇲🇽', 'Mexico City, Mexico 🇲🇽'], // Coyoacan
      [51.75, -1.25, 'England, United Kingdom 🇬🇧', 'England, United Kingdom 🇬🇧'], // Oxford
      [1.37, 103.74, 'Woodlands, Singapore 🇸🇬', 'Singapore 🇸🇬'],
      [25.03, 121.56, 'Taipei, Taiwan 🇹🇼', 'Taiwan 🇹🇼'],
      [22.32, 114.17, 'Hong Kong, China 🇨🇳', 'Hong Kong, China 🇨🇳'],
      [22.20, 113.54, 'Macau, China 🇨🇳', 'Macau, China 🇨🇳'],
      [21.31, -157.99, '‘Ewa Beach, HI 🇺🇸', 'Hawaii 🇺🇸'],
      [50.66, 30.40, 'Kyiv, Ukraine 🇺🇦', 'Kyiv, Ukraine 🇺🇦'],
    ];

    for (let i = 0; i < testdata.length; i++) {
      const data = testdata[i];
      console.log(data);

      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', 1)
        .send({
          latitude: data[0],
          longitude: data[1],
        });
      expect(res.status).to.equal(200);

      user = await User.findById('1');
      user.metrics.numPendingReports = 0;
      await user.save();

      // location should be returned
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(1);
      expect(res.body.profiles[0]._id).to.equal('1');
      expect(res.body.profiles[0].location).to.equal(data[2]);
    }

	  // hide city
	  res = await request(app)
	  .put('/v1/user/hideCity')
	  .set('authorization', 1)
	  .send({
	    hideCity: true,
	  });
	  expect(res.status).to.equal(200);

    for (let i = 0; i < testdata.length; i++) {
      const data = testdata[i];
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', 1)
        .send({
          latitude: data[0],
          longitude: data[1],
        });
      expect(res.status).to.equal(200);

      user = await User.findById('1');
      user.metrics.numPendingReports = 0;
      await user.save();

      // location without city should be returned
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(1);
      expect(res.body.profiles[0]._id).to.equal('1');
      expect(res.body.profiles[0].location).to.equal(data[3]);
    }
  });

  it('various locations - UK user', async () => {
    // User 0: United Kingdom
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 51.75,
        longitude: -1.25,
      });
    expect(res.status).to.equal(200);

	  // show city
	  res = await request(app)
	  .put('/v1/user/hideCity')
	  .set('authorization', 1)
	  .send({
	    hideCity: false,
	  });
	  expect(res.status).to.equal(200);

    testdata = [
      // [latitude, longitude, location, location without city]
      [21.30, -157.85, 'Hawaii, United States 🇺🇸', 'Hawaii, United States 🇺🇸'],
      [21.40, -157.74, 'Hawaii, United States 🇺🇸', 'Hawaii, United States 🇺🇸'],
      [40.73, -74.00, 'New York, United States 🇺🇸', 'New York, United States 🇺🇸'],
      [40.63, -73.95, 'New York, United States 🇺🇸', 'New York, United States 🇺🇸'],
      [29.66, -95.34, 'Texas, United States 🇺🇸', 'Texas, United States 🇺🇸'],
      [41.31, -72.92, 'Connecticut, United States 🇺🇸', 'Connecticut, United States 🇺🇸'],
      [19.35, -99.15, 'Mexico City, Mexico 🇲🇽', 'Mexico City, Mexico 🇲🇽'],
      [51.75, -1.25, 'Oxford, England 🇬🇧', 'England 🇬🇧'],
      [1.37, 103.74, 'Woodlands, Singapore 🇸🇬', 'Singapore 🇸🇬'],
      [25.03, 121.56, 'Taipei, Taiwan 🇹🇼', 'Taiwan 🇹🇼'],
      [22.32, 114.17, 'Hong Kong, China 🇨🇳', 'Hong Kong, China 🇨🇳'],
      [22.20, 113.54, 'Macau, China 🇨🇳', 'Macau, China 🇨🇳'],
      [50.66, 30.40, 'Kyiv, Ukraine 🇺🇦', 'Kyiv, Ukraine 🇺🇦'],
    ];

    for (let i = 0; i < testdata.length; i++) {
      const data = testdata[i];
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', 1)
        .send({
          latitude: data[0],
          longitude: data[1],
        });
      expect(res.status).to.equal(200);

      user = await User.findById('1');
      user.metrics.numPendingReports = 0;
      await user.save();

      // location should be returned
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(1);
      expect(res.body.profiles[0]._id).to.equal('1');
      expect(res.body.profiles[0].location).to.equal(data[2]);
    }

	  // hide city
	  res = await request(app)
	  .put('/v1/user/hideCity')
	  .set('authorization', 1)
	  .send({
	    hideCity: true,
	  });
	  expect(res.status).to.equal(200);

    for (let i = 0; i < testdata.length; i++) {
      const data = testdata[i];
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', 1)
        .send({
          latitude: data[0],
          longitude: data[1],
        });
      expect(res.status).to.equal(200);

      user = await User.findById('1');
      user.metrics.numPendingReports = 0;
      await user.save();

      // location without city should be returned
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(1);
      expect(res.body.profiles[0]._id).to.equal('1');
      expect(res.body.profiles[0].location).to.equal(data[3]);
    }
  });

  it('teleport', async () => {
    // User 0: Honolulu
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        distance: 30,
      });
    expect(res.status).to.equal(200);

    // User 1: Brooklyn
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 40.63,
        longitude: -73.95,
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        distance: 30,
      });
    expect(res.status).to.equal(200);

    // no recommendations - too far away
    for (let uid = 0; uid < 1; uid++) {
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(0);
    }

    // teleport for user 1
    user = await User.findOne({ _id: 1 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .put('/v1/teleport/location')
      .set('authorization', 1)
      .send({
        latitude: 21.40,
        longitude: -157.74,
      });
    expect(res.status).to.equal(200);

    // now users find each other
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[0].location).to.equal('Kailua, HI 🇺🇸');
    expect(res.body.profiles[0].teleport).to.equal(true);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
    expect(res.body.profiles[0].location).to.equal('Honolulu, HI 🇺🇸');
    expect(res.body.profiles[0].teleport).to.equal(false);

    // Stop using teleport
    res = await request(app)
      .delete('/v1/teleport/location')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    // no recommendations - too far away
    for (let uid = 0; uid < 1; uid++) {
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      expect(res.body.profiles.length).to.equal(0);
    }

    // teleport expired
    res = await request(app)
      .put('/v1/teleport/location')
      .set('authorization', 1)
      .send({
        latitude: 21.40,
        longitude: -157.74,
      });
    expect(res.status).to.equal(200);
    user = await User.findOne({ _id: 1 });
    user.premiumExpiration = Date.now() - 100;
    await user.save();
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    // no recommendations - too far away
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
  });

  it('local only when upgrading to premium', async () => {
    // different locations
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 51.75,
        longitude: -1.25,
      });
    expect(res.status).to.equal(200);

    // user 1 - local
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({ distance: 1 });
    expect(res.status).to.equal(200);

    // upgrade to new version
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.10.22' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.local).to.equal(true);
    expect(res.body.user.preferences.global).to.equal(true);
    expect(res.body.user.preferences.countries).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.10.22' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.local).to.equal(true);
    expect(res.body.user.preferences.global).to.equal(false);
    expect(res.body.user.preferences.countries).to.equal();

    // no profiles
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    // purchase premium
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 1)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    // no profiles
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    // if distance was accidentally set, it should be cleared on app restart
    user = await User.findOne({ _id: 1 });
    user.preferences.distance = 12500;
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.local).to.equal(true);
    expect(res.body.user.preferences.global).to.equal(false);

    // no profiles
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
  });

  it('local/global and country filter', async () => {
    // same location
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    // user 1 - local
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({ distance: 1 });
    expect(res.status).to.equal(200);

    // upgrade to new version
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.10.22' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.local).to.equal(true);
    expect(res.body.user.preferences.global).to.equal(true);
    expect(res.body.user.preferences.countries).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.10.22' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.local).to.equal(true);
    expect(res.body.user.preferences.global).to.equal(false);
    expect(res.body.user.preferences.countries).to.equal();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // 30 miles away -  should still see because local mode now means 50 miles
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 21.59,
        longitude: -158.1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 0 global only
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        local: false,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // user 1 - GB
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 51.75,
        longitude: -1.25,
      });
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.metrics.numPendingReports = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    // user 1 - global
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        global: true,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // not premium - can't select countries
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 1)
      .send({
        countries: ['AU'],
      });
    expect(res.status).to.equal(403);

    // user 1 - restrict countries
    user = await User.findOne({ _id: 1 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 1)
      .send({
        countries: ['AU'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    // user 1 - clear country filter
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 1)
      .send({
        countries: null,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);

    // set country filter again
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 1)
      .send({
        countries: ['AU'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.10.22' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.local).to.equal(true);
    expect(res.body.user.preferences.global).to.equal(true);
    expect(res.body.user.preferences.countries).to.eql(['AU']);

    // clear country filter
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 1)
      .send({
        countries: [],
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.10.22' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.countries).to.eql();

    // set country filter
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 1)
      .send({
        countries: ['AU'],
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.10.22' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.countries).to.eql(['AU']);

    // premium expires - country filter should be reset
    user = await User.findOne({ _id: 1 });
    user.premiumExpiration = Date.now() - ********;
    await user.save();
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.10.22' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.local).to.equal(true);
    expect(res.body.user.preferences.global).to.equal(true);
    expect(res.body.user.preferences.countries).to.equal();
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 1)
      .send({
        countries: ['AU'],
      });
    expect(res.status).to.equal(403);
  });

  it('country filter mutual match', async () => {
    // user 0 - US
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 40.7,
        longitude: -74,
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    user.metrics.numPendingReports = 0;
    await user.save();

    // user 1 - GB
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 51.75,
        longitude: -1.25,
      });
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.metrics.numPendingReports = 0;
    await user.save();

    // upgrade to new version
    for (let i = 0; i <= 2; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.10.22' });
      expect(res.status).to.equal(200);
      expect(res.body.user.preferences.local).to.equal(true);
      expect(res.body.user.preferences.global).to.equal(true);
      expect(res.body.user.preferences.countries).to.equal();
    }

    // user 1 - restrict countries
    user = await User.findOne({ _id: 1 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 1)
      .send({
        countries: ['AU'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    // user 0 changes location to AU
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: -33.86,
        longitude: 151.2,
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    user.metrics.numPendingReports = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
  });

  it('remove country filter for country group 1', async () => {

    // user 0 - Delhi
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 28.7,
        longitude: 77.1,
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    user.metrics.numPendingReports = 0;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.removeCountryFilter).to.equal(true);

    // user 1 - GB
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 51.75,
        longitude: -1.25,
      });
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.metrics.numPendingReports = 0;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.removeCountryFilter).to.equal(false);

    // user 2 - Manila
    await createUser(2);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 2)
      .send({
        latitude: 14.6,
        longitude: 120.9,
      });
    expect(res.status).to.equal(200);

    user = await User.findById('2');
    user.metrics.numPendingReports = 0;
    await user.save();

    // user 3 - USA
    await createUser(3);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 3)
      .send({
        latitude: 40.7,
        longitude: -74,
      });
    expect(res.status).to.equal(200);

    user = await User.findById('3');
    user.metrics.numPendingReports = 0;
    await user.save();

    // user 4 - Mumbai
    await createUser(4);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 4)
      .send({
        latitude: 19.07,
        longitude: 72.87,
      });
    expect(res.status).to.equal(200);

    user = await User.findById('4');
    user.metrics.numPendingReports = 0;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 4);
    expect(res.status).to.equal(200);
    expect(res.body.removeCountryFilter).to.equal(true);

    // upgrade to new version
    for (let i = 0; i <= 4; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.10.22' });
      expect(res.status).to.equal(200);
      expect(res.body.user.preferences.local).to.equal(true);
      expect(res.body.user.preferences.global).to.equal(true);
      expect(res.body.user.preferences.countries).to.equal();
    }

    // user 0 tries to set country filter to GB
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 0)
      .send({
        countries: ['GB'],
      });
    expect(res.status).to.equal(200);

    // country filter ignored, user 0 sees India user instead
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body.profiles);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('4');

    // user 1 sets country filter to India
    user = await User.findOne({ _id: 1 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 1)
      .send({
        countries: ['IN'],
      });
    expect(res.status).to.equal(200);

    // country filter applied, user 1 sees only IN user
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log(res.body.profiles);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(['0','4']);

    // user 5 - Manila timezone
    await createUser(5);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 5)
      .send({
        timezone: 'Asia/Manila',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 5);
    expect(res.status).to.equal(200);
    expect(res.body.removeCountryFilter).to.equal(true);

    // upgrade to new version
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 5)
      .send({ appVersion: '1.10.22' });
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.local).to.equal(true);
    expect(res.body.user.preferences.global).to.equal(true);
    expect(res.body.user.preferences.countries).to.equal();

    // user 5 tries to set country filter to GB
    user = await User.findOne({ _id: 5 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 5)
      .send({
        countries: ['GB'],
      });
    expect(res.status).to.equal(200);

    // country filter ignored, user 5 sees PH user instead
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 5);
    expect(res.status).to.equal(200);
    console.log(res.body.profiles);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('2');
  });
});

describe('interplanetary', () => {
  it('interplanetary allows finding faraway users', async () => {
    // new users are always interplanetary by default
    for (let uid = 0; uid < 2; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          gender: [
            'female',
          ],
          personality: [
            'ISTP',
          ],
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: 1990,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          answers: {},
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', uid)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);
    }

    // User 0: Honolulu, HI
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    // User 1: England
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 51.75,
        longitude: -1.25,
      });
    expect(res.status).to.equal(200);

    // manually set one user's interplanetary flag to false
    // interplanetary mode should still work
    const user = await User.findById(0);
    user.interplanetaryMode = false;
    await user.save();

    // check interplanetary flag returned to frontend
    for (let uid = 0; uid < 2; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);
      expect(res.body.user.interplanetaryMode).to.equal(true);
      expect(res.body.user.preferences.distance).to.equal(12500);
    }

    // each user should be returned in the other's daily profiles
    await User.ensureIndexes();
    for (let uid = 0; uid < 2; uid++) {
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);

      expect(res.body.profiles.length).to.equal(1);
      expect(res.body.profiles[0]._id).to.equal(`${(uid + 1) % 2}`);
    }

    // adjust distance preference for one user
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        distance: 200,
      });

    // both users won't see any profiles
    for (let uid = 0; uid < 2; uid++) {
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      expect(res.body.profiles.length).to.equal(0);
    }

    // change distance preference back to infinity
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        distance: 12500,
      });

    // both users should see profiles now
    for (let uid = 0; uid < 2; uid++) {
      res = await request(app)
        .get('/v1/user/dailyProfiles')
        .set('authorization', uid);
      expect(res.body.profiles.length).to.equal(1);
      expect(res.body.profiles[0]._id).to.equal(`${(uid + 1) % 2}`);
    }
  });

  /*
  it('reserve 50% for users from same country', async function() {

    const numUsers = DAILY_PROFILE_LIMIT * 2 + 2;
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .get('/v1/user')
        .set('authorization', uid)
      expect(res.status).to.equal(200);
    }
    for (let uid = 0; uid < numUsers; uid++) {
      user = await User.findOne({_id: uid});
      user.gender = 'female';
      user.birthday = new Date(1990,1,1);
      user.personality = {
        mbti: "ISTP"
      };
      user.preferences = {
        distance: 12500,
        gender: ["female"],
        minAge: 18,
        maxAge: 200,
        personality: ["ISTP"]
      };
      user.pictures = ['picture']
      user.save()
    }

    // England
    for (let uid = 0; uid < DAILY_PROFILE_LIMIT; uid++) {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          "latitude": 51.75,
          "longitude": -1.25
        })
      expect(res.status).to.equal(200);
    }

    // Honolulu
    for (let uid = DAILY_PROFILE_LIMIT;
      uid < 2 * DAILY_PROFILE_LIMIT + 1;
      uid++)
    {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          "latitude": 21.30,
          "longitude": -157.85
        })
      expect(res.status).to.equal(200);
    }

    // Texas
    for (let uid = 2 * DAILY_PROFILE_LIMIT + 1;
      uid < 2 * DAILY_PROFILE_LIMIT + 2;
      uid++) {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          "latitude": 29.66,
          "longitude": -95.34
        })
      expect(res.status).to.equal(200);
    }

    await User.ensureIndexes();

    // England user should get local profiles, then 1 USA
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT)
    for (let i = 0; i < DAILY_PROFILE_LIMIT - 1; i++) {
      expect(res.body.profiles[i].location).to.equal('Oxford, England 🇬🇧')
    }
    expect(res.body.profiles[DAILY_PROFILE_LIMIT - 1].location).to.match(/United States/)

    // Honolulu user should only get local profiles
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', DAILY_PROFILE_LIMIT)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT)
    for (let i = 0; i < DAILY_PROFILE_LIMIT; i++) {
      expect(res.body.profiles[i].location).to.equal('Honolulu, HI 🇺🇸')
    }

    // Texas user should get half Honolulu, half England
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 2 * DAILY_PROFILE_LIMIT + 1)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT)
    let numHonolulu = 0;
    let numEngland = 0;
    for (let i = 0; i < DAILY_PROFILE_LIMIT; i++) {
      if (res.body.profiles[i].location == 'Honolulu, HI 🇺🇸') {
        numHonolulu++;
      }
      if (res.body.profiles[i].location == 'England, United Kingdom 🇬🇧') {
        numEngland++;
      }
    }
    expect(numHonolulu).to.equal(DAILY_PROFILE_LIMIT / 2);
    expect(numEngland).to.equal(DAILY_PROFILE_LIMIT / 2);
  });

  it('teleport interplanetary', async function() {

    const numUsers = DAILY_PROFILE_LIMIT * 2;
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .get('/v1/user')
        .set('authorization', uid)
      expect(res.status).to.equal(200);
    }
    for (let uid = 0; uid < numUsers; uid++) {
      user = await User.findOne({_id: uid});
      user.gender = 'female';
      user.birthday = new Date(1990,1,1);
      user.personality = {
        mbti: "ISTP"
      };
      user.preferences = {
        distance: 12500,
        gender: ["female"],
        minAge: 18,
        maxAge: 200,
        personality: ["ISTP"]
      };
      user.pictures = ['picture']
      user.save()
    }

    // England
    for (let uid = 0; uid < DAILY_PROFILE_LIMIT / 2; uid++) {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          "latitude": 51.75,
          "longitude": -1.25
        })
      expect(res.status).to.equal(200);
    }

    // Honolulu
    for (let uid = DAILY_PROFILE_LIMIT / 2;
      uid < DAILY_PROFILE_LIMIT;
      uid++)
    {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          "latitude": 21.30,
          "longitude": -157.85
        })
      expect(res.status).to.equal(200);
    }

    // Tokyo
    for (let uid = DAILY_PROFILE_LIMIT;
      uid < 1.5 * DAILY_PROFILE_LIMIT;
      uid++) {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          "latitude": 35.67,
          "longitude": 139.65
        })
      expect(res.status).to.equal(200);
    }

    // Singapore
    for (let uid = 1.5 * DAILY_PROFILE_LIMIT;
      uid < 2 * DAILY_PROFILE_LIMIT;
      uid++) {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          "latitude": 1.35,
          "longitude": 103.82
        })
      expect(res.status).to.equal(200);
    }

    await User.ensureIndexes();

    // England user teleports to California - should get half USA
    user = await User.findOne({_id: 0});
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .put('/v1/teleport/location')
      .set('authorization', 0)
      .send({
        "latitude": 34.05,
        "longitude": -118.24
      })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT)
    numHonolulu = 0;
    for (let i = 0; i < DAILY_PROFILE_LIMIT; i++) {
      if (res.body.profiles[i].location == 'Honolulu, HI 🇺🇸') {
        numHonolulu++;
      }
    }
    expect(numHonolulu).to.equal(DAILY_PROFILE_LIMIT / 2);

  });
  */

  it('country tiers', async () => {
    const numUsers = 2 * DAILY_PROFILE_LIMIT;
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
    }
    for (let uid = 0; uid < numUsers; uid++) {
      user = await User.findOne({ _id: uid });
      user.gender = 'female';
      user.personality = {
        mbti: 'ISTP',
      };
      await user.save();

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', uid)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: new Date().getFullYear() - 31,
          month: 4,
          day: 1,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          global: true,
          gender: ['female'],
          minAge: 18,
          maxAge: 200,
          personality: ['ISTP'],
        });
      expect(res.status).to.equal(200);

      if (uid % 2) {
        // Honolulu
        res = await request(app)
          .put('/v1/user/location')
          .set('authorization', uid)
          .send({
            latitude: 21.30,
            longitude: -157.85,
          });
        expect(res.status).to.equal(200);
      } else {
        // Manila, Philippines
        res = await request(app)
          .put('/v1/user/location')
          .set('authorization', uid)
          .send({
            latitude: 14.60,
            longitude: 120.10,
          });
        expect(res.status).to.equal(200);
      }
    }

    // User 0: England
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 51.75,
        longitude: -1.25,
      });
    expect(res.status).to.equal(200);

    // User 1: Davao, Philippines
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 7.07,
        longitude: 125.61,
      });
    expect(res.status).to.equal(200);

    await User.ensureIndexes();

    // User 0 should only get USA profiles
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT - 1);
    for (let i = 0; i < DAILY_PROFILE_LIMIT - 1; i++) {
      expect(res.body.profiles[i].location).to.equal('Hawaii, United States 🇺🇸');
    }

    // User 1 should only get Philippines profiles
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT - 1);
    for (let i = 0; i < DAILY_PROFILE_LIMIT - 1; i++) {
      expect(res.body.profiles[i].location).to.equal('Sabang, Central Luzon 🇵🇭');
    }

    /*
    // users 0 and 1 teleport and switch places, user 1 should now get USA profiles
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .put('/v1/teleport/location')
      .set('authorization', 0)
      .send({
        latitude: 20.59,
        longitude: 78.96,
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 1 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .put('/v1/teleport/location')
      .set('authorization', 1)
      .send({
        latitude: 51.75,
        longitude: -1.25,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT - 1);
    for (let i = 0; i < DAILY_PROFILE_LIMIT - 1; i++) {
      expect(res.body.profiles[i].location).to.equal('Hawaii, United States 🇺🇸');
    }
    */
  });

  /*
  it('sort by num passes received', async function() {

    let numUsers = 2*DAILY_PROFILE_LIMIT;
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .get('/v1/user')
        .set('authorization', uid)
      expect(res.status).to.equal(200);
    }
    for (let uid = 0; uid < numUsers; uid++) {
      // Honolulu
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          "latitude": 21.30,
          "longitude": -157.85
        })
      expect(res.status).to.equal(200);

      user = await User.findOne({_id: uid});
      user.gender = 'female';
      user.birthday = new Date(1990,1,1);
      user.personality = {
        mbti: "ISTP"
      };
      user.preferences = {
        distance: 12500,
        gender: ["female"],
        minAge: 18,
        maxAge: 200,
        personality: ["ISTP"]
      };
      user.pictures = ['picture']
      user.metrics.numPassesReceivedCurrentDay = undefined;
      user.save()
    }

    // User 0: England
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        "latitude": 51.75,
        "longitude": -1.25
      })
    expect(res.status).to.equal(200);

    for (let i = 0; i < DAILY_PROFILE_LIMIT - 1; i++) {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 0)
        .send({
          "user": (DAILY_PROFILE_LIMIT + i).toString(),
        })
    }

    for (let i = 0; i < DAILY_PROFILE_LIMIT - 1; i++) {
      res = await request(app)
        .patch('/v1/user/pass')
        .set('authorization', 1)
        .send({
          "user": (DAILY_PROFILE_LIMIT + i).toString(),
        })
    }

    for (let i = 0; i < DAILY_PROFILE_LIMIT; i++) {
      res = await request(app)
        .patch('/v1/user/pass')
        .set('authorization', 2)
        .send({
          "user": i.toString(),
        })
    }

    await User.ensureIndexes();

    // User 0 should see last user
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1)
    expect(res.body.profiles[0]._id).to.equal((2*DAILY_PROFILE_LIMIT-1).toString())

  });
  */

  it('mutual matching - do not insert users multiple times', async function() {

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.48' });
    expect(res.status).to.equal(200);

    const numUsers = 4;
    for (let uid = 0; uid < numUsers; uid++) {
      await initUser(uid);
    }

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(3)
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(['1','2','3']);

    for (let i = 1; i <= 3; i++) {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', i)
        .send({
          user: '0',
        });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(3)
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(['1','2','3']);

    user = await User.findById('0');
    user.metrics.currentDayResetTime = null;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(3)
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(['1','2','3']);
  });

  it('mutual matching - do not filter on profileModifiedAt when getting usersWhoLiked', async function() {

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.48' });
    expect(res.status).to.equal(200);

    const numUsers = 4;
    for (let uid = 0; uid < numUsers; uid++) {
      await initUser(uid);
    }

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(3);
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(['1','2','3']);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // set exhausted at to a future time
    user = await User.findById('0');
    user.recommendationsExhaustedAt = Date.now() + ********;
    user.recentRecommendations = [] //reset recentRecommendations because recommendationsExhaustedAt not used when loading recentRecommendations
    user.metrics.currentDayResetTime = null;
    await user.save();

    // regular profiles should be filtered by exhausted at, but users who liked should not
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
  });

  it('insert users who liked in daily profiles', async function() {

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.48' });
    expect(res.status).to.equal(200);

    const numUsers = 10;
    for (let uid = 0; uid < numUsers; uid++) {
      await initUser(uid);
    }

    // England
    for (let uid = 0; uid < 5; uid++) {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          "latitude": 51.75,
          "longitude": -1.25
        })
      expect(res.status).to.equal(200);
    }

    for (let uid = 5; uid < 10; uid++) { // within 100miles radis to fall in users preference radius
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          "latitude": 52.75,
          "longitude": -1.25
        })
      expect(res.status).to.equal(200);
    }

    // user 0 should get only local profiles
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(4)
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(['1','2','3','4']);

    // user 0 receives 1 like from distant profile, inserted
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 5)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(4)
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(['1','2','3','5']);

    // user 0 receives 2 more likes, all inserted after refresh
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 6)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 7)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(4)
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(['1','2','3','5']);

    user = await User.findById('0');
    user.metrics.currentDayResetTime = null;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(4)
    expect(res.body.profiles.map(x => x._id)).to.have.same.members(['1','5','6','7']);
  });

  it('users hiding location should be shown for mutual matching', async function() {

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.48' });
    expect(res.status).to.equal(200);

    const numUsers = 2;
    for (let uid = 0; uid < numUsers; uid++) {
      await initUser(uid);
    }

    // user 1 hides location
    user = await User.findOne({ _id: 1 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();

    res = await request(app)
      .put('/v1/user/hideLocation')
      .set('authorization', 1)
      .send({ hideLocation: true });
    expect(res.status).to.equal(200);

    // user 1 not visible
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    // user 1 sends a like
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // now user 1 should be shown
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1)
    expect(res.body.profiles[0]._id).to.equal('1');
  });
});

describe('verified filter', async () => {
  beforeEach(async () => {
    constants.hideUnverifiedUsers.restore();
    sinon.stub(constants, 'hideUnverifiedUsers').returns(true);

    const numUsers = 2;
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.11.6' });
      expect(res.status).to.equal(200);
    }
    for (let uid = 0; uid < numUsers; uid++) {
      // Honolulu
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          latitude: 21.30,
          longitude: -157.85,
        });
      expect(res.status).to.equal(200);

      user = await User.findOne({ _id: uid });
      user.gender = 'female';
      user.personality = {
        mbti: 'ISTP',
      };
      await user.save();

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', uid)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: new Date().getFullYear() - 31,
          month: 4,
          day: 1,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          distance: 12500,
          gender: ['female'],
          minAge: 18,
          maxAge: 200,
          personality: ['ISTP'],
        });
      expect(res.status).to.equal(200);
    }
  });

  it('show verified only', async () => {
    // enabled hide unverified users, so no profiles returned
    let res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    // disable hide unverified users
    res = await request(app)
      .patch('/v1/user/hideUnverifiedUsers')
      .set('authorization', 0)
      .send({ hideUnverifiedUsers: false });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/hideUnverifiedUsers')
      .set('authorization', 1)
      .send({ hideUnverifiedUsers: false });
    expect(res.status).to.equal(200);

    // able to get profile
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');

    // not verified - not able to set verified filter
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({ showVerifiedOnly: true });
    expect(res.status).to.equal(403);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.showVerifiedOnly).to.equal(false);

    // set verified
    user = await User.findOne({ _id: 0 });
    user.verification.status = 'verified';
    res = await user.save();

    // now able to set verified filter
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({ showVerifiedOnly: true });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.preferences.showVerifiedOnly).to.equal(true);

    // enable hide unverified users
    res = await request(app)
      .patch('/v1/user/hideUnverifiedUsers')
      .set('authorization', 0)
      .send({ hideUnverifiedUsers: true });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/hideUnverifiedUsers')
      .set('authorization', 1)
      .send({ hideUnverifiedUsers: true });
    expect(res.status).to.equal(200);

    // user 0 does not see user 1, but user 1 can see user 0
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');

    // set user 1 to verified
    user = await User.findOne({ _id: 1 });
    user.verification.status = 'verified';
    res = await user.save();

    // now able to see user 1
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
  });

  it('app_657_v2 show verified only', async () => {
    // initial state - both unverified
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    // set both verified
    user = await User.findOne({ _id: 0 });
    user.verification.status = 'verified';
    res = await user.save();

    user = await User.findOne({ _id: 1 });
    user.verification.status = 'verified';
    res = await user.save();

    // now both users can see each other
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('0');
  });
});

it('show top profiles', async () => {
  const numUsers = 3 * DAILY_PROFILE_LIMIT;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.11.7' });
    expect(res.status).to.equal(200);
  }
  for (let uid = 0; uid < numUsers; uid++) {
    // Honolulu
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: uid });
    user.gender = 'female';
    user.personality = {
      mbti: 'ISTP',
    };
    await user.save();

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: new Date().getFullYear() - 31,
        month: 4,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        distance: 12500,
        gender: ['female'],
        minAge: 18,
        maxAge: 200,
        personality: ['ISTP'],
      });
    expect(res.status).to.equal(200);
  }

  // Set top profiles to high total score, low decayed score
  const topProfileId = (numUsers - 1).toString();
  user = await User.findOne({ _id: topProfileId });
  user.scores.totalScore = 40;
  user.scores.decayedScore = 0;
  user.scores.totalScore2 = 4;
  user.scores.decayedScore2 = 0;
  user.metrics['numActionsReceived'] = 35;
  user.scores['likeRatioScore'] = 19;
  res = await user.save();

  const topProfile2Id = (numUsers - 2).toString();
  user = await User.findOne({ _id: topProfile2Id });
  user.scores.decayedScore = 0;
  user.scores.decayedScore2 = 0;
  res = await user.save();

  // remove picture
  user = await User.findOne({ _id: 0 });
  user.pictures = [];
  res = await user.save();

  // should get top profile
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(Math.ceil(DAILY_PROFILE_LIMIT / 3));
  expect(res.body.profiles[0]._id).to.equal(topProfileId);

  // upload picture
  user = await User.findOne({ _id: 0 });
  user.pictures = ['picture'];
  res = await user.save();

  // should not get top profile
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  expect(res.body.profiles[0]._id).to.not.equal(topProfileId);

  // Pass all the recommendations
  profiles = res.body.profiles;
  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
  }

  // set next top profile
  user = await User.findOne({ _id: topProfile2Id });
  user.scores.totalScore = 39;
  user.scores.decayedScore = 0;
  user.scores.totalScore2 = 3;
  user.scores.decayedScore2 = 0;
  res = await user.save();

  // After acting on all recommendations, user should get next top profile
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(Math.ceil(DAILY_PROFILE_LIMIT / 3));
  expect(res.body.profiles[0]._id).to.equal(topProfile2Id);
  profiles = res.body.profiles;

  // not able to like or pass any more
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: profiles[0]._id,
    });
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal(true);

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 0)
    .send({
      user: profiles[0]._id,
    });
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal(true);

  // can still dm
  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 0)
    .send({
      user: profiles[0]._id,
      message: 'Hi',
      price: 50,
    });
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal();
  const messages = await Message.find();
  expect(messages.length).to.equal(1);
});

it('A/B test - limit likes', async () => {
  const numUsers = 3 * DAILY_PROFILE_LIMIT;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.29' });
    expect(res.status).to.equal(200);
  }
  for (let uid = 0; uid < numUsers; uid++) {
    // Honolulu
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: uid });
    user.gender = 'female';
    user.personality = {
      mbti: 'ISTP',
    };
    await user.save();

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: new Date().getFullYear() - 31,
        month: 4,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        distance: 12500,
        gender: ['female'],
        minAge: 18,
        maxAge: 200,
        personality: ['ISTP'],
      });
    expect(res.status).to.equal(200);
  }

  // Set user 0 to test group 0
  user = await User.findOne({ _id: 0 });
  user.config.limit_likes = false;
  res = await user.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  // Pass all the recommendations
  profiles = res.body.profiles;
  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
  }

  // After acting on all recommendations, user should get no more
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  console.log(res.body);
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal(true);

  // Set user 1 to test group 1
  user = await User.findOne({ _id: 1 });
  user.config.limit_likes = true;
  res = await user.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  expect(res.body.dailyLimitExceeded).to.equal();

  // Like all the recommendations
  profiles = res.body.profiles;
  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal();
  }

  // should still have full recommendations
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  console.log(res.body);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  expect(res.body.dailyLimitExceeded).to.equal();
  profiles = res.body.profiles;

  // not able to like any more
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: profiles[0]._id,
    });
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal(true);

  // can still pass or dm
  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 1)
    .send({
      user: profiles[0]._id,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 1)
    .send({
      user: profiles[1]._id,
      message: 'Hi',
      price: 50,
    });
  expect(res.status).to.equal(200);

  // Set user 2 to test group 1
  user = await User.findOne({ _id: 2 });
  user.config.limit_likes = true;
  res = await user.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  // Pass all recommendations
  profiles = res.body.profiles;
  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 2)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
  }

  // should still have full recommendations
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 2);
  console.log(res.body);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  expect(res.body.dailyLimitExceeded).to.equal();

  // Like up to the limit
  profiles = res.body.profiles;
  for (let i = 0; i < DAILY_LIKES_LIMIT; i++) {
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 2)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal();
  }

  // should have full recommendations
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 2);
  console.log(res.body);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  expect(res.body.dailyLimitExceeded).to.equal();
  profiles = res.body.profiles;

  // not able to like any more
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 2)
    .send({
      user: profiles[0]._id,
    });
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal(true);

  // can still pass or dm
  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 2)
    .send({
      user: profiles[0]._id,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 2)
    .send({
      user: profiles[1]._id,
      message: 'Hi',
      price: 50,
    });
  expect(res.status).to.equal(200);

  // set user 2 to premium
  user = await User.findOne({ _id: 2 });
  user.premiumExpiration = Date.now() + ********;
  await user.save();

  // should be able to send likes now
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  profiles = res.body.profiles;

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 2)
    .send({
      user: profiles[0]._id,
    });
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal();
});

it('A/B test - limit likes for 2 days', async () => {
  const numUsers = 3 * DAILY_PROFILE_LIMIT;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.29' });
    expect(res.status).to.equal(200);
  }
  for (let uid = 0; uid < numUsers; uid++) {
    // Honolulu
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: uid });
    user.gender = 'female';
    user.personality = {
      mbti: 'ISTP',
    };
    await user.save();

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: new Date().getFullYear() - 31,
        month: 4,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        distance: 12500,
        gender: ['female'],
        minAge: 18,
        maxAge: 200,
        personality: ['ISTP'],
      });
    expect(res.status).to.equal(200);
  }

  // Set user 0 to test group 0
  user = await User.findOne({ _id: 0 });
  user.config.limit_likes_2_days = false;
  res = await user.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  // Pass all the recommendations
  profiles = res.body.profiles;
  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
  }

  // After acting on all recommendations, user should get no more
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  console.log(res.body);
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal(true);

  // Set user 1 to test group 1
  user = await User.findOne({ _id: 1 });
  user.config.limit_likes_2_days = true;
  res = await user.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  expect(res.body.dailyLimitExceeded).to.equal();

  // Like all the recommendations
  profiles = res.body.profiles;
  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal();
  }

  // should still have full recommendations
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  console.log(res.body);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  expect(res.body.dailyLimitExceeded).to.equal();
  profiles = res.body.profiles;

  // not able to like any more
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: profiles[0]._id,
    });
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal(true);

  // can still pass or dm
  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 1)
    .send({
      user: profiles[0]._id,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 1)
    .send({
      user: profiles[1]._id,
      message: 'Hi',
      price: 50,
    });
  expect(res.status).to.equal(200);

  // Set user 2 to test group 1 and created 2 days ago
  user = await User.findOne({ _id: 2 });
  user.config.limit_likes_2_days = true;
  user.createdAt = moment().subtract(2, 'days').toDate();
  res = await user.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  // Pass all recommendations
  profiles = res.body.profiles;
  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 2)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
  }

  // After acting on all recommendations, user should get no more
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  console.log(res.body);
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal(true);
});

/*
describe('A/B test - daily limit 30', function() {

  const numUsers = 35;

  beforeEach(async () => {

    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          "gender": [
              "female"
          ],
          "personality": [
              "ISTP"
          ]
        })
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({"gender": "female"})
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          "year": 1990,
          "month": 1,
          "day": 1
        })
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          "answers": {}
        })
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          "longitude": 0,
          "latitude": 51
        })

      // mock upload a picture
      let user = await User.findOne({_id: uid});
      user.pictures.push("picture")
      res = await user.save()
    }
    await User.ensureIndexes();
  });

  it('limit 10 - A/B test group 0', async function() {

    // Set user 0 to test group 0
    user = await User.findOne({_id: 0});
    user.createdAt = new Date(2022, 1, 1, 0, 0, 0, 0);
    res = await user.save()

    // 10 profiles returned
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(10);

    // Like all the recommendations
    profiles = res.body.profiles;
    for (let i = 0; i < profiles.length; i++) {
      res = await request(app)
        .patch('/v1/user/like')
        .set('authorization', 0)
        .send({
          "user": profiles[i]._id,
          "message": "hi"
        })
      expect(res.status).to.equal(200);
    }

    // After acting on all recommendations, user should get no more
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
      console.log(res.body)
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal(true);
  });

  it('limit 30 - A/B test group 1', async function() {

    // Set user 0 to test group 1
    user = await User.findOne({_id: 0});
    user.createdAt = new Date(2022, 1, 1, 0, 0, 0, 1);
    res = await user.save()

    // 30 profiles returned
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(30);

    // Like all the recommendations
    profiles = res.body.profiles;
    for (let i = 0; i < profiles.length; i++) {
      res = await request(app)
        .patch('/v1/user/like')
        .set('authorization', 0)
        .send({
          "user": profiles[i]._id,
          "message": "hi"
        })
      expect(res.status).to.equal(200);
    }

    // After acting on all recommendations, user should get no more
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
      console.log(res.body)
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal(true);
  });

  it('limit 10 - old user', async function() {

    // Set user 0 to old user
    user = await User.findOne({_id: 0});
    user.createdAt = new Date(2020, 1, 1, 0, 0, 0, 0);
    res = await user.save()

    // 10 profiles returned
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(10);

    // Like all the recommendations
    profiles = res.body.profiles;
    for (let i = 0; i < profiles.length; i++) {
      res = await request(app)
        .patch('/v1/user/like')
        .set('authorization', 0)
        .send({
          "user": profiles[i]._id,
          "message": "hi"
        })
      expect(res.status).to.equal(200);
    }

    // After acting on all recommendations, user should get no more
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0)
      console.log(res.body)
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal(true);
  });
});
*/

// describe('hide ghost users', function() {
//
//  const numUsers = 2;
//
//  beforeEach(async () => {
//
//    for (let uid = 0; uid < numUsers; uid++) {
//      res = await request(app)
//        .put('/v1/user/initApp')
//        .set('authorization', uid)
//      res = await request(app)
//        .patch('/v1/user/preferences')
//        .set('authorization', uid)
//        .send({
//          "gender": [
//              "female"
//          ],
//          "personality": [
//              "ISTP"
//          ]
//        })
//      res = await request(app)
//        .put('/v1/user/gender')
//        .set('authorization', uid)
//        .send({"gender": "female"})
//      res = await request(app)
//        .put('/v1/user/birthday')
//        .set('authorization', uid)
//        .send({
//          "year": 1990,
//          "month": 1,
//          "day": 1
//        })
//      res = await request(app)
//        .put('/v1/user/quizAnswers')
//        .set('authorization', uid)
//        .send({
//          "answers": {}
//        })
//      res = await request(app)
//        .put('/v1/user/location')
//        .set('authorization', uid)
//        .send({
//          "longitude": 0,
//          "latitude": 51
//        })
//
//      // mock upload a picture
//      let user = await User.findOne({_id: uid});
//      user.pictures.push("picture")
//      res = await user.save()
//    }
//    await User.ensureIndexes();
//  });
//
//
//  it('hide ghost user - A/B test group 1', async function() {
//
//    // Set user 0 to hide ghost user
//    user = await User.findOne({_id: 0});
//    user.createdAt = new Date(2022, 1, 1, 0, 0, 0, 1);
//    res = await user.save()
//
//    // Make user 1 a ghost user
//    user = await User.findOne({_id: 1});
//    user.updatedAt = new Date(2000, 1, 1);
//    res = await user.save({ timestamps: false })
//
//    // No users returned
//    res = await request(app)
//      .get('/v1/user/dailyProfiles')
//      .set('authorization', 0)
//    expect(res.status).to.equal(200);
//    expect(res.body.profiles.length).to.equal(0);
//
//    // Make user 1 active
//    user = await User.findOne({_id: 1});
//    user.updatedAt = new Date();
//    res = await user.save()
//
//    // User 1 returned
//    res = await request(app)
//      .get('/v1/user/dailyProfiles')
//      .set('authorization', 0)
//    expect(res.status).to.equal(200);
//    expect(res.body.profiles.length).to.equal(1)
//    expect(res.body.profiles[0]._id).to.equal('1')
//  });
//
//  it('no hide ghost user - A/B test group 0', async function() {
//
//    // Set user 0 to not hide ghost user
//    user = await User.findOne({_id: 0});
//    user.createdAt = new Date(2022, 1, 1, 0, 0, 0, 0);
//    res = await user.save()
//
//    // Make user 1 a ghost user
//    user = await User.findOne({_id: 1});
//    user.updatedAt = new Date(2000, 1, 1);
//    res = await user.save({ timestamps: false })
//
//    // User returned
//    res = await request(app)
//      .get('/v1/user/dailyProfiles')
//      .set('authorization', 0)
//    expect(res.status).to.equal(200);
//    expect(res.body.profiles.length).to.equal(1)
//    expect(res.body.profiles[0]._id).to.equal('1')
//
//    // Make user 1 active
//    user = await User.findOne({_id: 1});
//    user.updatedAt = new Date();
//    res = await user.save()
//
//    // User 1 returned
//    res = await request(app)
//      .get('/v1/user/dailyProfiles')
//      .set('authorization', 0)
//    expect(res.status).to.equal(200);
//    expect(res.body.profiles.length).to.equal(1)
//    expect(res.body.profiles[0]._id).to.equal('1')
//  });
//
//  it('no hide ghost user - old user', async function() {
//
//    // Set user 0 to not hide ghost user
//    user = await User.findOne({_id: 0});
//    user.createdAt = new Date(2020, 1, 1, 0, 0, 0, 0);
//    res = await user.save()
//
//    // Make user 1 a ghost user
//    user = await User.findOne({_id: 1});
//    user.updatedAt = new Date(2000, 1, 1);
//    res = await user.save({ timestamps: false })
//
//    // User returned
//    res = await request(app)
//      .get('/v1/user/dailyProfiles')
//      .set('authorization', 0)
//    expect(res.status).to.equal(200);
//    expect(res.body.profiles.length).to.equal(1)
//    expect(res.body.profiles[0]._id).to.equal('1')
//
//    // Make user 1 active
//    user = await User.findOne({_id: 1});
//    user.updatedAt = new Date();
//    res = await user.save()
//
//    // User 1 returned
//    res = await request(app)
//      .get('/v1/user/dailyProfiles')
//      .set('authorization', 0)
//    expect(res.status).to.equal(200);
//    expect(res.body.profiles.length).to.equal(1)
//    expect(res.body.profiles[0]._id).to.equal('1')
//  });
// });

/*
Note: there is a bug that causes age preference filter to break if user's
age is greater than 105. This is unlikely to have any practical impact.
*/
it('age preference', async () => {
  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
      .send({ appVersion: '1.10.29' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', i)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', i)
      .send({ mbti: 'ESTJ' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', i)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', i)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', i)
      .send({
        friends: ['female'],
      });
    expect(res.status).to.equal(200);
  }

  // user 0: age 100, minAge 44
  res = await request(app)
    .put('/v1/user/birthday')
    .set('authorization', 0)
    .send({
      year: new Date().getFullYear() - 100,
      month: 4,
      day: 1,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      minAge: 44,
    });
  expect(res.status).to.equal(200);

  // user 1: age 20
  res = await request(app)
    .put('/v1/user/birthday')
    .set('authorization', 1)
    .send({
      year: new Date().getFullYear() - 20,
      month: 4,
      day: 1,
    });
  expect(res.status).to.equal(200);

  // user 0 should not see user 1
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);
});

it('gender missing', async () => {
  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
      .send({ appVersion: '1.10.29' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', i)
      .send({ mbti: 'ESTJ' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', i)
      .send({
        year: new Date().getFullYear() - 31,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', i)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', i)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', i)
      .send({
        friends: ['female'],
      });
    expect(res.status).to.equal(200);
  }

  // user 0 does not have gender - should not see profiles
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .put('/v1/user/gender')
    .set('authorization', 1)
    .send({ gender: 'female' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);
});

it('optimize out of recommendations', async () => {
  for (let i = 0; i < 3; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
      .send({ appVersion: '1.10.29' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', i)
      .send({ mbti: 'ESTJ' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', i)
      .send({
        year: new Date().getFullYear() - 31,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', i)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', i)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', i)
      .send({
        friends: ['female'],
        personality: ['ESTJ'],
      });
    expect(res.status).to.equal(200);
  }

  // set gender for user 0 and 1
  res = await request(app)
    .put('/v1/user/gender')
    .set('authorization', 0)
    .send({ gender: 'female' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/gender')
    .set('authorization', 1)
    .send({ gender: 'female' });
  expect(res.status).to.equal(200);

  // should get 1 profile
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);

  // should get same profile
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);

  // pass on profile
  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 0)
    .send({
      user: '1',
    });

  // out of profiles
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  // set gender for user 2
  res = await request(app)
    .put('/v1/user/gender')
    .set('authorization', 2)
    .send({ gender: 'female' });
  expect(res.status).to.equal(200);

  // should get 1 profile
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);

  // change user 2 personality
  res = await request(app)
    .put('/v1/user/personality')
    .set('authorization', 2)
    .send({ mbti: 'ISTJ' });
  expect(res.status).to.equal(200);

  // tick 10 days
  clock = sinon.useFakeTimers({now: Date.now()});
  clock.tick(10*24*60*60*1000);

  // should get 0 profile
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  // change preference
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      friends: ['female'],
      personality: ['ISTJ'],
    });
  expect(res.status).to.equal(200);

  // should get 1 profile
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);


  // make user 2 not visible
  user = await User.findById('2');
  user.viewableInDailyProfiles = false;
  await user.save();

  // user 2 hidden from daily boos
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  // tick 10 days
  clock.tick(10*24*60*60*1000);

  // make user 2 visible
  user = await User.findById('2');
  user.viewableInDailyProfiles = true;
  await user.save();

  // user 2 not hidden from daily boos
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);


  // pass on profile 2
  res = await request(app)
  .patch('/v1/user/pass')
  .set('authorization', 0)
  .send({
    user: '2',
  });

  // no recommendations for user
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  await UserMetadata.updateOne({ user: '0' }, { coins: coinsConstants.revivalCost });

  res = await request(app)
  .put('/v1/coins/revival')
  .send({ price: coinsConstants.revivalCost })
  .set('authorization', 0);
  expect(res.status).to.equal(200);

  //recommendationExhaustedAt becomes null
  expect(
    (await User.findOne({ _id: '0' }, { recommendationsExhaustedAt: 1 })).recommendationsExhaustedAt
  ).to.eql(null);

  // user 2 shows up again
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('2');

  clock.restore();
});

it('distance filter', async () => {
  for (let i = 0; i < 3; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
      .send({ appVersion: '1.10.29' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', i)
      .send({ mbti: 'ESTJ' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', i)
      .send({
        year: new Date().getFullYear() - 31,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', i)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', i)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', i)
      .send({
        friends: ['female'],
        personality: ['ESTJ'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', i)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);
  }

  // should get 2 profiles
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles[0].nearby).to.equal(true);

  // set distance filter for all users
  for (let i = 0; i < 3; i++) {
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', i)
      .send({
        distance2: 5,
        global: true,     // error from frontend, but backend should handle
      });
    expect(res.status).to.equal(200);
  }

  // should get 2 profiles
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);

  // set user 2 to 25 miles away
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 2)
    .send({
      latitude: 21.6,
      longitude: -158.1,
    });
  expect(res.status).to.equal(200);

  // should get 1 profile
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);

  // set distance filter
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      distance2: 50,
    });
  expect(res.status).to.equal(200);

  // should get 2 profiles
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);

  // set user 2 to far away
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 2)
    .send({
      latitude: 50,
      longitude: 50,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('2');
  user.metrics.numPendingReports = 0;
  await user.save();

  // should get 1 profile
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);

  // set user 0 distance filter to 100+
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      distance2: 100,
      global: false,   // error from frontend, but backend should handle
    });
  expect(res.status).to.equal(200);

  // should get 1 profiles
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);

  // set user 2 distance filter to 100+
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 2)
    .send({
      distance2: 100,
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  user.recentRecommendations = [];
  await user.save();

  // should get 2 profiles
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);

  // user 0 updates to 1.11.78
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.11.78' });
  expect(res.status).to.equal(200);

  // user 0 disables global mode
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      local: true,
      global: false,
    });
  expect(res.status).to.equal(200);

  // should get 1 profile
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('1');

  // user 0 disables local mode
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      local: false,
      global: true,
    });
  expect(res.status).to.equal(200);

  // should get 1 profile
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('2');
  expect(res.body.profiles[0].nearby).to.equal();
});

it('lat long distance', async () => {
  const locations = [
    ['Tokyo', [139, 35]],
    ['London', [0, 51]],
    ['San Francisco', [-122, 37]],
    ['Honolulu', [-157, 21]],
    ['Reykjavík', [-22, 64]],
    ['Wellington', [174, -41]],
    ['Singapore', [103, 1]],
  ];
  const distances = [500, 1000, 1500, 2000, 2500, 3000, 4000, 5000];

  for (const location of locations) {
    console.log(location);
    for (const distance of distances) {
      console.log(distance, locationLib.getLatLongQuery({coordinates:location[1]}, distance));
    }
  }
});

describe('show nearest first', async () => {

  beforeEach(async () => {
    for (let i = 0; i < 4; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.10.29' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/personality')
        .set('authorization', i)
        .send({ mbti: 'ESTJ' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', i)
        .send({
          year: new Date().getFullYear() - 31,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', i)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', i)
        .send({
          friends: ['female'],
          personality: ['ESTJ'],
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', i)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);
    }

    // User 0: San Diego
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 32.7157,
        longitude: -117.1611,
      });
    expect(res.status).to.equal(200);

    // User 1: Chicago
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 42,
        longitude: -87,
      });
    expect(res.status).to.equal(200);

    // User 2: Brooklyn
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 2)
      .send({
        latitude: 40.63,
        longitude: -73.95,
      });
    expect(res.status).to.equal(200);

    // User 3: Tijuana
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 3)
      .send({
        latitude: 32.5149,
        longitude: -117.0382,
      });
    expect(res.status).to.equal(200);
  });

  it('basic usage', async function() {

    // should be ordered by distance
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(3);
    expect(res.body.profiles[0]._id).to.equal('3');
    expect(res.body.profiles[1]._id).to.equal('1');
    expect(res.body.profiles[2]._id).to.equal('2');

    // user 2 moves to Las Vegas
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 2)
      .send({
        latitude: 36.17,
        longitude: -115.14,
      });
    expect(res.status).to.equal(200);

    user = await User.findById('2');
    user.metrics.numPendingReports = 0;
    await user.save();

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(3);
    expect(res.body.profiles[0]._id).to.equal('3');
    expect(res.body.profiles[1]._id).to.equal('2');
    expect(res.body.profiles[2]._id).to.equal('1');

    // user 0 sets country filter to US only
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 0)
      .send({
        countries: ['US'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('2');
    expect(res.body.profiles[1]._id).to.equal('1');

    // user 0 sets country filter to MX only
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 0)
      .send({
        countries: ['MX'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('3');

    // user 0 sets country filter to JP only
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    res = await request(app)
      .patch('/v1/user/preferences/countries')
      .set('authorization', 0)
      .send({
        countries: ['JP'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
  });

  it('local only', async function() {
    for (let i = 1; i < 4; i++) {
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', i)
        .send({
          local: true,
          global: false,
        });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('3');
  });
});

/*
it('interests loop', async () => {
  await initUser(0);
  await initUser(1);
  await initUser(2);

  // user 0 and 2 have same interest, user 1 has different interest
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({
      interestNames: ['kpop'],
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 2)
    .send({
      interestNames: ['kpop'],
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 1)
    .send({
      interestNames: ['chess'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles[0]._id).to.equal('2');
  expect(res.body.profiles[1]._id).to.equal('1');
});
*/

it('country border', async () => {
  await initUser(0);
  await initUser(1);
  await initUser(2);

  // user 0 - San Diego border
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 0)
    .send({
      latitude: 32.58,
      longitude: -117.06,
    });
  expect(res.status).to.equal(200);

  // user 1 - central San Diego
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 1)
    .send({
      latitude: 32.73,
      longitude: -117.13,
    });
  expect(res.status).to.equal(200);

  // user 2 - Tijuana border
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 2)
    .send({
      latitude: 32.53,
      longitude: -117.05,
    });
  expect(res.status).to.equal(200);

  for (let i = 0; i < 3; i++) {
    user = await User.findById(i.toString());
    user.metrics.numPendingReports = 0;
    await user.save();
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles[0]._id).to.equal('1');
  expect(res.body.profiles[1]._id).to.equal('2');
});

it('hide from keywords', async () => {
  await initUser(0);
  await initUser(1);
  await initUser(2);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);

  res = await request(app)
    .put(`/v1/user/description`)
    .set('authorization', 0)
    .send({
      description: 'hi',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put(`/v1/user/hideFromKeywords`)
    .set('authorization', 1)
    .send({
      hideFromKeywords: ['Hi '],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.hideFromKeywords).to.eql(['hi']);

  res = await request(app)
    .put(`/v1/user/hideFromKeywords`)
    .set('authorization', 2)
    .send({
      hideFromKeywords: ['3'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('2');

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('2');
});

it('hide from nearby', async () => {
  await initUser(0);
  await initUser(1);
  await initUser(2);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);

  res = await request(app)
    .put(`/v1/user/hideFromNearby`)
    .set('authorization', 1)
    .send({
      hideFromNearby: true,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.hideFromNearby).to.equal(true);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('2');

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 0)
    .send({
      latitude: 51.75,
      longitude: 0,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  user.metrics.numPendingReports = 0;
  await user.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
});

it('hide from nearby and local only', async () => {
  await initUser(0);
  await initUser(1);
  await initUser(2);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 1)
    .send({
      latitude: 21.7,
      longitude: -158.1,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  user.metrics.numPendingReports = 0;
  await user.save();

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 2)
    .send({
      latitude: 51.5072,
      longitude: 0,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('2');
  user.metrics.numPendingReports = 0;
  await user.save();

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      local: true,
      global: false,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put(`/v1/user/hideFromNearby`)
    .set('authorization', 0)
    .send({
      hideFromNearby: true,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('1');
});

it('show to verified only', async () => {
  await initUser(0);
  await initUser(1);

  user = await User.findOne({ _id: 0 });
  user.verification.status = 'verified';
  res = await user.save();

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({ showToVerifiedOnly: true });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.preferences.showToVerifiedOnly).to.equal(true);

  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 1)
    .query({ user: '0' });
  expect(res.status).to.equal(200);
  expect(res.body.user.preferences.showToVerifiedOnly).to.equal(true);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 1 });
  user.verification.status = 'verified';
  res = await user.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
});

it('local only out of profiles', async () => {
  await initUser(0);
  await initUser(1);

  // user 1: Brooklyn
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 1)
    .send({
      latitude: 40.63,
      longitude: -73.95,
    });
  expect(res.status).to.equal(200);

  await User.updateMany({}, {'metrics.numPendingReports': 0});

  // user 0 disables global mode
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      local: true,
      global: false,
    });
  expect(res.status).to.equal(200);

  // user 0 sees no local profiles
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  // user 0 changes location to brooklyn
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 0)
    .send({
      latitude: 40.63,
      longitude: -73.95,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  user.profileModifiedAt = new Date(2000, 1, 1);
  await user.save();

  // user 0 should see user 1
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
});

it('web profiles', async () => {
  // users 0 - 3 in the nearby location
  for (let i = 0; i < 4; i++) {
    await initUser(i);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', i)
      .send({
        latitude: 37.751,
        longitude: -97.822,
      });
    expect(res.status).to.equal(200);

    user = await User.findById(i.toString());
    user.metrics.numPendingReports = 0;
    await user.save();
  }

  // user 0 - male, score 4
  user = await User.findById('0');
  user.gender = 'male';
  user.scores.totalScore2 = 4;
  user.metrics['numActionsReceived'] = 35;
  user.scores['likeRatioScore'] = 19;
  await user.save();

  // user 1 - female, score 4
  user = await User.findById('1');
  user.gender = 'female';
  user.scores.totalScore2 = 4;
  user.metrics['numActionsReceived'] = 35;
  user.scores['likeRatioScore'] = 19;
  await user.save();

  // user 2 - male, score 3
  user = await User.findById('2');
  user.gender = 'male';
  user.scores.totalScore2 = 3;
  user.metrics['numActionsReceived'] = 35;
  user.scores['likeRatioScore'] = 19;
  await user.save();

  // user 3 - female, score 3
  user = await User.findById('3');
  user.gender = 'female';
  user.scores.totalScore2 = 3;
  user.metrics['numActionsReceived'] = 35;
  user.scores['likeRatioScore'] = 19;
  await user.save();

  // user 4 is in a different location
  await initUser(4);

  geoip.lookup.restore();
  const geoStub = sinon.stub(geoip, 'lookup').callsFake((ip) => {
    if (ip === '*************') {
      return {
        ll: [37.751, -97.822],
      };
    }
    return null;
  })

  const ip = '*************';
  res = await request(app)
    .get('/web/dailyProfiles')
    .set('X-Forwarded-For', ip)
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  expect(res.body.profiles[0]._id).to.equal('0');
  expect(res.body.profiles[1]._id).to.equal('1');
  expect(res.body.profiles[2]._id).to.equal('2');
  expect(res.body.profiles[3]._id).to.equal('3');

  geoStub.restore()

});

it('sort best for new user', async () => {

  sinon.stub(constants, 'getDailyProfileLimit').returns(2);

  for (let i = 0; i < 5; i++) {
    await initUser(i);
  }

  // user 0: score 0
  user = await User.findById('0');
  user.scores.totalScore2 = 0;
  await user.save();

  // user 1: score 4
  user = await User.findById('1');
  user.scores.totalScore2 = 4;
  user.metrics['numActionsReceived'] = 35;
  user.scores['likeRatioScore'] = 19;
  await user.save();

  // user 2: score 4
  user = await User.findById('2');
  user.scores.totalScore2 = 4;
  user.metrics['numActionsReceived'] = 35;
  user.scores['likeRatioScore'] = 19;
  await user.save();

  // user 3: score 0
  user = await User.findById('3');
  user.scores.totalScore2 = 0;
  await user.save();

  // user 4: score 0
  user = await User.findById('4');
  user.scores.totalScore2 = 0;
  await user.save();

  // no config = user 0 gets similar score profiles
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles.map(x => x._id)).to.have.members(['3', '4']);

  // config = user 0 gets top score profiles
  user = await User.findById('0');
  user.config.w1_show_best = true;
  user.recentRecommendations = [];
  await user.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles.map(x => x._id)).to.have.members(['1', '2']);

  // user 0 account is 2 days old = gets mix
  user = await User.findById('0');
  user.createdAt = moment().subtract(2, 'days').toDate();
  user.recentRecommendations = [];
  await user.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles.some(x => ['1', '2'].includes(x._id))).to.be.true;
});

/*
it('height', async () => {

  for (let i = 0; i < 2; i++) {
    await initUser(i);
  }

  // user 0 sets height
  res = await request(app)
    .put('/v1/user/height')
    .set('authorization', 0)
    .send({height: 60})
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.user.height).to.equal(60);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0].height).to.equal(60);

  // user 1 sets height preference
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 1)
    .send({
      minHeight: 65,
      maxHeight: 70,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.preferences.minHeight).to.equal(65);
  expect(res.body.user.preferences.maxHeight).to.equal(70);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 1)
    .send({
      minHeight: 45,
      maxHeight: 55,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 1)
    .send({
      minHeight: 55,
      maxHeight: 65,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);

  // clear height preferences
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 1)
    .send({
      minHeight: null,
      maxHeight: null,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.preferences.minHeight).to.equal(null);
  expect(res.body.user.preferences.maxHeight).to.equal(null);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
});
*/

it('removeDuplicateProfiles', async () => {

  for (let i = 0; i < 2; i++) {
    await initUser(i);
  }

  let profiles = [];

  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');
    profiles.push(res.body.profiles[0]);
  }

  expect(profiles.length).to.equal(2);
  let filteredProfiles = profilesLib.removeDuplicateProfiles(profiles);
  expect(filteredProfiles.length).to.equal(1);
  expect(filteredProfiles[0]._id).to.equal('1');
});

it('ethnicities', async () => {

  for (let i = 0; i < 2; i++) {
    await initUser(i);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
      .send({ appVersion: '1.13.24' })
    expect(res.status).to.equal(200);
  }

  // user 0 sets ethnicities
  res = await request(app)
    .put('/v1/user/ethnicities')
    .set('authorization', 0)
    .send({ethnicities: ['Asian','Chinese','Han Chinese']})
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.user.ethnicities).to.eql(['Asian','Chinese','Han Chinese']);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0].ethnicities).to.eql(['Asian','Chinese','Han Chinese']);

  // user 1 sets ethnicities preference
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 1)
    .send({
      ethnicities: ['Zhuang','Filipino','Latin'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.preferences.ethnicities).to.eql(['Zhuang','Filipino','Latin']);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 1)
    .send({
      ethnicities: ['Asian'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);

  // clear preferences
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 1)
    .send({
      ethnicities: null,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.preferences.ethnicities).to.equal();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
});

/*
it('secret admirer', async function() {
  constants.getKarmaTierSwipeLimits.restore();
  sinon.stub(constants, 'getKarmaTierSwipeLimits').returns([10]);
  sinon.stub(constants, 'getDailyProfileLimit').returns(10);
  clock = sinon.useFakeTimers();

  const numUsers = 11;
  for (let uid = 0; uid < numUsers; uid++) {
    await initUser(uid);
  }

  user = await User.findById('0');
  user.config.app_141 = true;
  user.config.app_165 = true;
  await user.save();

  for (let i = 1; i <= 5; i++) {
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', i)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);
  }

  // d1
  clock.tick(24 * msPerHour);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.currentDayMetrics.showSecretAdmirerOnSwipe).to.be.greaterThan(2);
  expect(user.currentDayMetrics.showSecretAdmirerOnSwipe).to.be.lessThan(6);
  console.log(user.currentDayMetrics.showSecretAdmirerOnSwipe);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  const profiles = res.body.profiles;
  console.log(profiles.map(x => x._id));
  expect(profiles.length).to.equal(10);
  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
    if (i+1 <= user.currentDayMetrics.showSecretAdmirerOnSwipe) {
      expect(res.body.missedPotentialMatch).to.equal();
    }
    if (i+1 == user.currentDayMetrics.showSecretAdmirerOnSwipe) {
      expect(res.body.showSecretAdmirer).to.equal(true);
    } else {
      expect(res.body.showSecretAdmirer).to.equal();
    }
  }

  user = await User.findById('0');
  expect(user.metrics.receivedSecretAdmirerOnDay).to.eql([1]);

  // d2
  clock.tick(24 * msPerHour);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.currentDayMetrics.showSecretAdmirerOnSwipe).to.equal(-1);

  // d4
  clock.tick(2*24 * msPerHour);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.currentDayMetrics.showSecretAdmirerOnSwipe).to.be.greaterThan(2);
  expect(user.currentDayMetrics.showSecretAdmirerOnSwipe).to.be.lessThan(6);

  // not triggered if user has fewer than 4 blurred likes
  res = await request(app)
    .patch('/v1/user/reject')
    .set('authorization', 0)
    .send({ user: '1' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/reject')
    .set('authorization', 0)
    .send({ user: '2' })
  expect(res.status).to.equal(200);

  // d5
  clock.tick(24 * msPerHour);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.currentDayMetrics.showSecretAdmirerOnSwipe).to.equal(-1);
});
*/

/*
it('automatic_revival_local_profiles', async () => {
  for (let i = 0; i < 3; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
      .send({ appVersion: '1.10.29' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', i)
      .send({ mbti: 'ESTJ' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', i)
      .send({
        year: new Date().getFullYear() - 31,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', i)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', i)
      .send({
        friends: ['female'],
        personality: ['ESTJ'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', i)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);
  }

  // user 0 and user 1 same location
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 0)
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 1)
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  // user 2 far away
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 2)
    .send({
      latitude: 50,
      longitude: 50,
    });
  expect(res.status).to.equal(200);

  // set config for user 0
  user = await User.findById('0');
  user.config.automatic_revival_local_profiles = true;
  await user.save();

  // should get 2 profiles
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles[0]._id).to.equal('1');
  expect(res.body.profiles[0].nearby).to.equal(true);
  expect(res.body.profiles[1]._id).to.equal('2');
  expect(res.body.profiles[1].nearby).to.equal();

  // pass on the local profile
  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 0)
    .send({ user: '1' });
  expect(res.status).to.equal(200);

  // automatic revival should happen
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles[0]._id).to.equal('1');
  expect(res.body.profiles[0].nearby).to.equal(true);
  expect(res.body.profiles[1]._id).to.equal('2');
  expect(res.body.profiles[1].nearby).to.equal();

  user = await User.findById('0');
  expect(user.metrics.automatic_revival_local_profiles).to.equal(1);
  expect(user.metrics.numLocalLikesSentBeforeRevival).to.equal(0);
  expect(user.metrics.numLocalPassesSentBeforeRevival).to.equal(1);
  expect(user.metrics.numLocalSwipesSentBeforeRevival).to.equal(1);
  expect(user.metrics.daysOnPlatformBeforeRevival).to.equal(0);

  // pass on the local profile again
  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 0)
    .send({ user: '1' });
  expect(res.status).to.equal(200);

  // automatic revival happens only once
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('2');
  expect(res.body.profiles[0].nearby).to.equal();

  user = await User.findById('0');
  expect(user.metrics.automatic_revival_local_profiles).to.equal(1);
  expect(user.metrics.numLocalLikesSentBeforeRevival).to.equal(0);
  expect(user.metrics.numLocalPassesSentBeforeRevival).to.equal(1);
  expect(user.metrics.numLocalSwipesSentBeforeRevival).to.equal(1);
  expect(user.metrics.daysOnPlatformBeforeRevival).to.equal(0);
});
*/

it('app_234', async () => {
  clock = sinon.useFakeTimers();

  for (let i = 0; i < 8; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
      .send({ appVersion: '1.13.49' })
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.49' })
  expect(res.status).to.equal(200);
  expect(res.body.freeSwipeFromQodReceived).to.equal(false);
  expect(res.body.freeSwipeReceivedForQods).to.eql([]);

  // swipe until run out of swipes
  for (let i = 1; i <= 4; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: i.toString(),
      });
    expect(res.status).to.equal(200);
    expect(res.body.dailyLimitExceeded).to.equal();
  }

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 0)
    .send({
      user: '5',
    });
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal(true);

  // comment on a qod
  qod = await createQuestion({
    createdAt: new Date(),
    text: 'qod 1',
    interestName: 'questions',
  });

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: qod._id,
      text: 'comment1',
      parentId: qod._id,
    });
  expect(res.status).to.equal(200);

  // receive additional swipe
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.freeSwipeFromQodReceived).to.equal(true);
  expect(res.body.freeSwipeReceivedForQods).to.eql([qod._id.toString()]);

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 0)
    .send({
      user: '5',
    });
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal();

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 0)
    .send({
      user: '6',
    });
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal(true);

  // only one free swipe per day
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: qod._id,
      text: 'comment2',
      parentId: qod._id,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 0)
    .send({
      user: '6',
    });
  expect(res.status).to.equal(200);
  expect(res.body.dailyLimitExceeded).to.equal(true);

  // free swipe resets after a day
  clock.tick(24*60*60*1000);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.freeSwipeFromQodReceived).to.equal(false);
  expect(res.body.freeSwipeReceivedForQods).to.eql([qod._id.toString()]);

  // cannot get a free swipe by commenting on old qod
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: qod._id,
      text: 'comment3',
      parentId: qod._id,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.freeSwipeFromQodReceived).to.equal(false);
  expect(res.body.freeSwipeReceivedForQods).to.eql([qod._id.toString()]);

  // free swipe by commenting on new qod
  qod2 = await createQuestion({
    createdAt: new Date(),
    text: 'qod 2',
    interestName: 'questions',
  });

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: qod2._id,
      text: 'comment4',
      parentId: qod2._id,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.freeSwipeFromQodReceived).to.equal(true);
  expect(res.body.freeSwipeReceivedForQods).to.eql([qod._id.toString(), qod2._id.toString()]);
});

describe('showUsersOutsideMyRange', () => {
  const numUsers = 5;

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);

      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          gender: ['female'],
          personality: ['ISTP'],
          distance2: 10,
          local: true,
          global: false,
        });

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'female' });

      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: 1990,
          month: 1,
          day: 1
        });

      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({
          answers: {}
        });

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', uid)
        .attach('image', validImagePath);

      expect(res.status).to.equal(200);
    }

    await User.ensureIndexes();
  });

  it('finding profiles with and without showUsersOutsideMyRange', async () => {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);

      res = await request(app)
      .get('/v1/user/preferences')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.showUsersOutsideMyRange).to.equal(true);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 33.1011,
        longitude: -115.6630
      });
    expect(res.status).to.equal(200);

    const testdata = [
      [33.1581, -116.6604, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
      [33.0750, -115.6655, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
      [47.6062, -122.3321, 'Seattle, WA 🇺🇸', 'Washington, USA 🇺🇸'],
      [32.4962, -115.3321, 'Mexicali, BC ', ' Baja California Mexico'] //mexico

    ];

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: testdata[0][0],
        longitude: testdata[0][1]
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 2)
      .send({
        latitude: testdata[1][0],
        longitude: testdata[1][1]
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 3)
    .send({
      latitude: testdata[2][0],
      longitude: testdata[2][1]
    });
  expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('2');

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '2',
        message: 'Hi',
        price: 50
      });
    expect(res.status).to.equal(200);

    //now checking outside the expanded range upto 100 miles
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        showUsersOutsideMyRange: false
      });
    expect(res.status).to.equal(200);
    expect(res.body.showUsersOutsideMyRange).to.equal(false);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.58' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        showUsersOutsideMyRange: true
      });
    expect(res.status).to.equal(200);
    expect(res.body.showUsersOutsideMyRange).to.equal(true);

    //version 1.13.58 and showUsersOutsideMyRange is true for user
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('1');

    res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 0)
    .send({
      user: '1',
      message: 'Hi',
      price: 50
    });
    expect(res.status).to.equal(200);

    res = await request(app) //mexico user
    .put('/v1/user/location')
    .set('authorization', 4)
    .send({
      latitude: testdata[3][0],
      longitude: testdata[3][1]
    });
    expect(res.status).to.equal(200);

    res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0]._id).to.equal('4');


    res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      gender: ['female'],
      personality: ['ISTP'],
      distance2: 10,
      local: true,
      global: false,
      sameCountryOnly: true
    });
    expect(res.status).to.equal(200);

    res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);
  });
});

it('return error on exclusionListFailed', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  user.exclusionListFailed = true;
  await user.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(403);
  expect(res.text).to.equal('Something went wrong. Please try again.');

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 0);
  expect(res.status).to.equal(403);
  expect(res.text).to.equal('Something went wrong. Please try again.');
});

it('new user should have currentExclusionListSize initialized to 0 and increment on actions', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 999)
    .send({ appVersion: '1.13.63' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 998)
    .send({ appVersion: '1.13.63' });
  expect(res.status).to.equal(200);

  let user = await User.findById('999');
  expect(user.metrics.currentExclusionListSize).to.equal(0);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 999)
    .send({ user: '998' });
  expect(res.status).to.equal(200);

  user = await User.findById('999');
  expect(user.metrics.currentExclusionListSize).to.equal(1);
});

/*
it('implement min distance for carrot algo with distance and limit checks', async () => {
  const createMatchingUser = async (uid) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        gender: ['female'],
        personality: ['ISTP'],
        distance2: 50,  // Increased distance for a wider match range
        local: true,
        global: false,
        sameCountryOnly: false,
      });

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: 'female' });

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1
      });

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {}
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);

    expect(res.status).to.equal(200);
  };

  // Create multiple matching users with varied distances
  for (let uid = 0; uid < 13; uid++) {
    await createMatchingUser(uid);
    let user = await User.findOne({ _id: uid })
    if(user._id == 0) user.config['app_161'] = true
    if(user._id == 1) user.config['app_161'] = false
    user.scores.decayedScore = 0;
    user.scores.totalScore2 = 1;
    user.scores.totalScore = 10;
    user.scores.decayedScore2 = 0;
    await user.save();
  }

  await User.ensureIndexes();

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.53' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.13.53' });
  expect(res.status).to.equal(200);

  const testLocations = [
    [33.1586, -116.6606, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1581, -116.6611, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1582, -116.6612, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1583, -116.6613, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1584, -116.6614, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1581, -116.6611, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1582, -116.6612, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1583, -116.6613, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1584, -116.6614, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1581, -116.6611, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1582, -116.6612, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1583, -116.6613, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1584, -116.6614, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1586, -116.6606, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'], // id 13, same loc as user 0
    [33.6580, -117.0000, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.6580, -115.9000, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    // [32.4962, -115.3321, 'Mexicali, BC', 'Baja California Mexico'], // Mexico
  ];

  for (let i = 0; i < 13; i++) {
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', i)
      .send({ latitude: testLocations[i][0], longitude: testLocations[i][1] });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  let user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);
  expect(user.metrics.displayedCarrotProfilesWithMinDistance).to.equal(undefined);

  profiles = res.body.profiles;

  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  profiles = res.body.profiles;

  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 1)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
  }

  for (let i = 13; i < 16; i++) {
    await createMatchingUser(i);
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', i)
      .send({ latitude: testLocations[i][0], longitude: testLocations[i][1] });
    expect(res.status).to.equal(200);
    if (i != 15) { //not making 15 carrot
      let user = await User.findOne({ _id: i });
      user.scores['totalScore2'] = 3;
      user.scores['decayedScore2'] = 3;
      await user.save();
    }
  }

  user = await User.findOne({ _id: 0 });
  expect(user.config.app_161).to.equal(true);
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);

  user = await User.findOne({ _id: 1 });
  expect(user.config.app_161).to.equal(false);
  expect(user.minDistanceForCarrotAlgo).to.equal(0);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles[0]._id).to.equal('14');
  expect(res.body.profiles[1]._id).to.equal('15');

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles[0]._id).to.equal('13');
  expect(res.body.profiles[1]._id).to.equal('14');
  expect(res.body.profiles[0].nearby).to.equal(true);

  // minDistanceForCarrotAlgo should not change
  user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);
  expect(user.metrics.displayedCarrotProfilesWithMinDistance).to.equal(true);

  user = await User.findOne({ _id: 1 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0);
  expect(user.metrics.displayedCarrotProfilesWithMinDistance).to.equal(undefined);

  user = await User.findOne({ _id: 14 });
  user.hideFromNearby = true;
  await user.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('15');

  user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      sameCountryOnly: true,
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    global: true,
  });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(undefined);
});

it('carrot and regular algo check', async () => {
  const createMatchingUser = async (uid) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        minAge: 18,
        maxAge: 200,
        dating: ['male', 'female'],
        friends: ['male', 'female'],
        distance: null,
        distance2: null,
        local: true,
        global: true,
        showUsersOutsideMyRange: true,
        sameCountryOnly: false,
      });

      res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: 'female' });

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1
      });

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {}
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);

    expect(res.status).to.equal(200);
  };

  // Create multiple matching users with varied distances
  for (let uid = 0; uid < 40; uid++) {
    await createMatchingUser(uid);
    let user = await User.findOne({ _id: uid })
    if(user._id == 0) user.config['app_161'] = true
    user.scores.decayedScore = 0;
    user.scores.totalScore2 = 1;
    user.scores.totalScore = 10;
    user.scores.decayedScore2 = 0;
    await user.save();
  }

  await User.ensureIndexes();

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.65' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.13.65' });
  expect(res.status).to.equal(200);

  const testLocations = [
    [33.1586, -116.6606, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1581, -116.6611, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1582, -116.6612, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1583, -116.6613, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1584, -116.6614, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1581, -116.6611, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1582, -116.6612, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1583, -116.6613, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1584, -116.6614, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1581, -116.6611, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1582, -116.6612, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1583, -116.6613, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1584, -116.6614, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.1586, -116.6606, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'], // id 13, same loc as user 0
    [33.6580, -117.0000, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    [33.6580, -115.9000, 'Imperial County, CA 🇺🇸', 'California, USA 🇺🇸'],
    // [32.4962, -115.3321, 'Mexicali, BC', 'Baja California Mexico'], // Mexico
  ];

  for (let i = 0; i < 13; i++) {
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', i)
      .send({ latitude: testLocations[i][0], longitude: testLocations[i][1] });
    expect(res.status).to.equal(200);
  }
  for (let i = 13; i < 40; i++) {
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', i)
      .send({ latitude: 33.6580, longitude: -113.11 });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  let user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);
  expect(user.metrics.displayedCarrotProfilesWithMinDistance).to.equal(undefined);

  profiles = res.body.profiles;

  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
  }

  for (let i = 40; i < 45; i++) {
    await createMatchingUser(i);
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', i)
      .send({ latitude: 33.6580, longitude: -115.9000 });
    expect(res.status).to.equal(200);
      let user = await User.findOne({ _id: i });
      user.scores['totalScore2'] = 3;
      user.scores['decayedScore2'] = 3;
      await user.save();
  }

  for (let i = 45; i < 50; i++) {
    await createMatchingUser(i);
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', i)
      .send({ latitude: 33.6580, longitude: -115.9000 });
      expect(res.status).to.equal(200);
      let user = await User.findOne({ _id: i });
      user.scores['totalScore2'] = 3;
      user.scores['decayedScore2'] = 3;
      await user.save();
  }

  user = await User.findOne({ _id: 0 });
  expect(user.config.app_161).to.equal(true);
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles[0]._id).to.equal('40');
  expect(res.body.profiles[1]._id).to.equal('41');

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
expect(res.status).to.equal(200);
expect(res.body.profiles.length).to.equal(2);
expect(res.body.profiles[0]._id).to.equal('41');

  // minDistanceForCarrotAlgo should not change
  user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);
  expect(user.metrics.displayedCarrotProfilesWithMinDistance).to.equal(true);

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    minAge: 18,
    maxAge: 200,
    dating: ['male', 'female'],
    friends: ['male', 'female'],
    distance: null,
    distance2: null,
    local: true,
    global: true,
    showUsersOutsideMyRange: true,
    sameCountryOnly: false,
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);

  let [res2,res1] = await Promise.all([
    request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0),
  request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0)
  ])
  expect(res2.status).to.equal(200);
  expect(res2.body.profiles.length).to.equal(2);
  expect(res2.body.profiles[0]._id).to.equal('42');
  expect(res1.status).to.equal(200);
  expect(res1.body.profiles.length).to.equal(2);
  expect(res1.body.profiles[0]._id).to.equal('42');

  user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);
  expect(user.preferences.global).to.equal(true);

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
        "personality": [
          "INFP",
          "INFJ",
          "ENFP",
          "ENFJ",
          "INTP",
          "INTJ",
          "ENTP",
          "ENTJ",
          "ISFP",
          "ISFJ",
          "ESFP",
          "ESFJ",
          "ISTP",
          "ISTJ",
          "ESTP",
          "ESTJ"
        ],
        "gender": null,
        "distance": null,
        "minAge": 18,
        "maxAge": 200,
        "purpose": null,
        "dating": [
          "female"
        ],
        "friends": [
          "female",
          "male"
        ],
        "local": true,
        "global": true,
        "countries": null,
        "interests": null,
        "showVerifiedOnly": false,
        "enneagrams": null,
        "horoscopes": null,
        "interestNames": null,
        "languages": null,
        "keywords": [],
        "exercise": null,
        "educationLevel": null,
        "drinking": null,
        "smoking": null,
        "kids": null,
        "religion": null,
        "distance2": null,
        "showToVerifiedOnly": null,
        "bioLength": null,
        "sameCountryOnly": true,
        "ethnicities": null,
        "minHeight": null,
        "maxHeight": null,
        "relationshipStatus": null,
        "datingSubPreferences": null,
        "showUsersOutsideMyRange": true
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);

  user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(0.044379296754477555);

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    global: false,
  });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.minDistanceForCarrotAlgo).to.equal(undefined);
});
*/

it('App 380 issue with revival no souls', async () => {

  const createMatchingUser = async (uid) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        gender: ['female'],
        personality: ['ISTP'],
        distance2: 50,
        local: true,
        global: false,
        sameCountryOnly: false,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({ answers: {} });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);
  };

  for (let uid = 0; uid < 20; uid++) {
    await createMatchingUser(uid);
  }

  let res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  let profiles = res.body.profiles;

  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: profiles[i]._id });
    expect(res.status).to.equal(200);
  }

  const clock = sinon.useFakeTimers(Date.now());
  clock.tick(2 * 24 * 3600 * 1000); // Advance time by 2 days

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  profiles = res.body.profiles;

  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: profiles[i]._id });
    expect(res.status).to.equal(200);
  }

  for (let uid = 20; uid < 24; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', uid)
      .send({ user: '0' });
    expect(res.status).to.equal(200);
  }

  const usersWhoLiked = await UsersWhoLiked.findOne({ user: '0' });
  expect(usersWhoLiked.usersWhoLiked).to.eql(['20', '21', '22', '23']);

  for (let uid = 20; uid < 24; uid++) {
    const user = await User.findOne({ _id: uid });
    await user.deleteAccount();
  }

  clock.tick(2 * 24 * 3600 * 1000);

  let userMetadata = await UserMetadata.findOne({ user: 0 });
  userMetadata.coins = 2 * coinsConstants.revivalCost;
  userMetadata = await userMetadata.save();

  res = await request(app)
    .put('/v1/coins/revival')
    .send({ price: coinsConstants.revivalCost })
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  clock.tick(2 * 24 * 3600 * 1000);
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  // Reset recommendationsExhaustedAt to null if any
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({ distance2: 98 });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
});

it('only revive passed profiles not liked blocked profiles', async () => {

  const createMatchingUser = async (uid) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        gender: ['female'],
        personality: ['ISTP'],
        distance2: 50,
        local: true,
        global: false,
        sameCountryOnly: false,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({ answers: {} });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);
  };

  for (let uid = 0; uid < 5; uid++) {
    await createMatchingUser(uid);
  }
  let user0 = await User.findOne({ _id: 0 });
  user0.premiumExpiration = new Date('2025-11-11');
  await user0.save();

  let userMetadata = await UserMetadata.findOne({ user: 0 });
  userMetadata.coins = 2 * coinsConstants.revivalCost;
  userMetadata = await userMetadata.save();

  let res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  let profiles = res.body.profiles;

  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: profiles[i]._id });
    expect(res.status).to.equal(200);
  }

  //send like to profile 2
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({ user: '2' });
  expect(res.status).to.equal(200);

    //send DM to profile 3
  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 0)
    .send({
      user: '3',
      message: 'Hi'
    });
  expect(res.status).to.equal(200);

  //block user 4
  res = await request(app)
    .patch('/v1/user/block')
    .set('authorization', 0)
    .send({ user: '4' })
  expect(res.status).to.equal(200);

  await createMatchingUser(5);

    //get blocked by user 5
    res = await request(app)
    .patch('/v1/user/block')
    .set('authorization', 5)
    .send({ user: '0' })
  expect(res.status).to.equal(200);

  let userExclusion = await ExclusionList.findOne({ user: '0' })
  expect(userExclusion.exclusionList).to.eql(['1', '2', '3', '4', '5']);

  res = await request(app)
    .put('/v1/coins/revival')
    .send({ price: coinsConstants.revivalCost })
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  userExclusion = await ExclusionList.findOne({ user: '0' })
  expect(userExclusion.exclusionList).to.eql(['3', '2', '5', '4']);
});

it('App 563 issue with no souls and then souls with distance filter', async () => {

  const createMatchingUser = async (uid) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.13.69' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        friends: ["male"],
        dating:['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: 'male' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({ answers: {} });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // setting half of the user group to more than 50 miles and other half to less than 50miles
    if (uid < 10) {
      res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.0586, longitude: -116.6606 });
      expect(res.status).to.equal(200);
    } else {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({ latitude: 33.8586, longitude: -116.6606 });
      expect(res.status).to.equal(200);
    }
  };

  for (let uid = 0; uid < 20; uid++) {
    await createMatchingUser(uid);
  }

  //set all profileModifiedAt to before date and createdAt last two 2 days
  const twoDaysBeforeNow = new Date();
  twoDaysBeforeNow.setDate(twoDaysBeforeNow.getDate() - 2);
  await User.updateMany({ },{ $set: { createdAt: twoDaysBeforeNow, profileModifiedAt: twoDaysBeforeNow } })

  let res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  let profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

  res = await request(app)
  .patch('/v1/user/pass')
  .set('authorization', 0)
  .send({ user: profiles[0]._id });
  expect(res.status).to.equal(200);

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
      "bioLength": null,
      "countries": null,
      "dating": ["male"],
      "datingSubPreferences": null,
      "distance": null,
      "distance2": null,
      "drinking": null,
      "educationLevel": null,
      "enneagrams": null,
      "ethnicities": null,
      "exercise": null,
      "friends": [
          "male",
          "female"
      ],
      "gender": null,
      "global": false,
      "horoscopes": null,
      "interestNames": null,
      "interests": null,
      "keywords": [
      ],
      "kids": null,
      "languages": null,
      "local": true,
      "maxAge": 200,
      "maxHeight": null,
      "minAge": 18,
      "minHeight": null,
      "personality": [
          "INFP",
          "INFJ",
          "ENFP",
          "ENFJ",
          "INTP",
          "INTJ",
          "ENTP",
          "ENTJ",
          "ISFP",
          "ISFJ",
          "ESFP",
          "ESFJ",
          "ISTP",
          "ISTJ",
          "ESTP",
          "ESTJ"
      ],
      "purpose": null,
      "relationshipStatus": null,
      "religion": null,
      "sameCountryOnly": null,
      "showToVerifiedOnly": null,
      "showUsersOutsideMyRange": true,
      "showVerifiedOnly": false,
      "smoking": null
  });
  expect(res.status).to.equal(200);

  user0 = await User.findOne({ _id: 0 });
  user0.premiumExpiration = new Date('2025-11-11');
  await user0.save();

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: profiles[i]._id });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: profiles[i]._id });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: profiles[i]._id });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

  for (let i = 0; i < 2; i++) {
  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 0)
    .send({ user: profiles[i]._id });
  expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
      "bioLength": null,
      "countries": null,
      "dating": ["male"],
      "datingSubPreferences": null,
      "distance": null,
      "distance2": 95,
      "drinking": null,
      "educationLevel": null,
      "enneagrams": null,
      "ethnicities": null,
      "exercise": null,
      "friends": [
          "male",
          "female"
      ],
      "gender": null,
      "global": false,
      "horoscopes": null,
      "interestNames": null,
      "interests": null,
      "keywords": [
      ],
      "kids": null,
      "languages": null,
      "local": true,
      "maxAge": 200,
      "maxHeight": null,
      "minAge": 18,
      "minHeight": null,
      "personality": [
          "INFP",
          "INFJ",
          "ENFP",
          "ENFJ",
          "INTP",
          "INTJ",
          "ENTP",
          "ENTJ",
          "ISFP",
          "ISFJ",
          "ESFP",
          "ESFJ",
          "ISTP",
          "ISTJ",
          "ESTP",
          "ESTJ"
      ],
      "purpose": null,
      "relationshipStatus": null,
      "religion": null,
      "sameCountryOnly": null,
      "showToVerifiedOnly": null,
      "showUsersOutsideMyRange": true,
      "showVerifiedOnly": false,
      "smoking": null
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

});

it('App 563 issue with no souls sort best and then souls with distance filter', async () => {

  const createMatchingUser = async (uid) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.13.69' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        friends: ["male"],
        dating:['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: 'male' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({ answers: {} });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // setting half of the user group to more than 50 miles and other half to less than 50miles
    if (uid < 10) {
      res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.0586, longitude: -116.6606 });
      expect(res.status).to.equal(200);
    } else {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({ latitude: 33.8586, longitude: -116.6606 });
      expect(res.status).to.equal(200);
    }
  };

  for (let uid = 0; uid < 7; uid++) {
    await createMatchingUser(uid);
    if (uid < 4) {
      let user = await User.findOne({ _id: uid })
      user.scores['totalScore2'] = 3;
      user.scores['decayedScore2'] = 3;
      user.metrics['numActionsReceived'] = 35;
      user.scores['likeRatioScore'] = 19;
      await user.save();
    }
  }

  //set all profileModifiedAt to before date and createdAt last two 2 days
  const twoDaysBeforeNow = new Date();
  twoDaysBeforeNow.setDate(twoDaysBeforeNow.getDate() - 2);
  await User.updateMany({ },{ $set: { createdAt: twoDaysBeforeNow, profileModifiedAt: twoDaysBeforeNow } })

  let res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  let profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
      "bioLength": null,
      "countries": null,
      "dating": ["male"],
      "datingSubPreferences": null,
      "distance": null,
      "distance2": null,
      "drinking": null,
      "educationLevel": null,
      "enneagrams": null,
      "ethnicities": null,
      "exercise": null,
      "friends": [
          "male",
          "female"
      ],
      "gender": null,
      "global": false,
      "horoscopes": null,
      "interestNames": null,
      "interests": null,
      "keywords": [
      ],
      "kids": null,
      "languages": null,
      "local": true,
      "maxAge": 200,
      "maxHeight": null,
      "minAge": 18,
      "minHeight": null,
      "personality": [
          "INFP",
          "INFJ",
          "ENFP",
          "ENFJ",
          "INTP",
          "INTJ",
          "ENTP",
          "ENTJ",
          "ISFP",
          "ISFJ",
          "ESFP",
          "ESFJ",
          "ISTP",
          "ISTJ",
          "ESTP",
          "ESTJ"
      ],
      "purpose": null,
      "relationshipStatus": null,
      "religion": null,
      "sameCountryOnly": null,
      "showToVerifiedOnly": null,
      "showUsersOutsideMyRange": true,
      "showVerifiedOnly": false,
      "smoking": null
  });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: profiles[i]._id });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
      "bioLength": null,
      "countries": null,
      "dating": ["male"],
      "datingSubPreferences": null,
      "distance": null,
      "distance2": 95,
      "drinking": null,
      "educationLevel": null,
      "enneagrams": null,
      "ethnicities": null,
      "exercise": null,
      "friends": [
          "male",
          "female"
      ],
      "gender": null,
      "global": false,
      "horoscopes": null,
      "interestNames": null,
      "interests": null,
      "keywords": [
      ],
      "kids": null,
      "languages": null,
      "local": true,
      "maxAge": 200,
      "maxHeight": null,
      "minAge": 18,
      "minHeight": null,
      "personality": [
          "INFP",
          "INFJ",
          "ENFP",
          "ENFJ",
          "INTP",
          "INTJ",
          "ENTP",
          "ENTJ",
          "ISFP",
          "ISFJ",
          "ESFP",
          "ESFJ",
          "ISTP",
          "ISTJ",
          "ESTP",
          "ESTJ"
      ],
      "purpose": null,
      "relationshipStatus": null,
      "religion": null,
      "sameCountryOnly": null,
      "showToVerifiedOnly": null,
      "showUsersOutsideMyRange": true,
      "showVerifiedOnly": false,
      "smoking": null
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  profiles = res.body.profiles;

  //top profile added to exclusion
  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2); // topProfilesExclusionList reset
  profiles = res.body.profiles;

  await new Promise((resolve) => setTimeout(resolve, 100));

});

it('app_566 new carrot defination', async () => {

  const createMatchingUser = async (uid) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        minAge: 18,
        maxAge: 200,
        dating: ['male', 'female'],
        friends: ['male', 'female'],
        distance: null,
        distance2: null,
        local: true,
        global: true,
        showUsersOutsideMyRange: true,
        sameCountryOnly: false,
      });

      res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: 'female' });

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1
      });

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {}
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);
  };

  // Create multiple matching users with varied distances
  for (let uid = 0; uid < 10; uid++) {
    await createMatchingUser(uid);
    let user = await User.findOne({ _id: uid })
    user.scores.decayedScore = 0;
    user.scores.totalScore2 = 1;
    user.scores.totalScore = 10;
    user.scores.decayedScore2 = 0;
    user.metrics['numActionsReceived'] = 35;
    user.scores['likeRatioScore'] = 10;
    await user.save();
  }

  await User.ensureIndexes();

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.65' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  profiles = res.body.profiles;

  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
  }

  profiles = res.body.profiles;

  for (let i = 40; i < 45; i++) {
    await createMatchingUser(i);
      let user = await User.findOne({ _id: i });
      user.scores['totalScore2'] = 3;
      user.scores['decayedScore2'] = 3;
      await user.save();
  }

  for (let i = 45; i < 48; i++) {
    await createMatchingUser(i);
      let user = await User.findOne({ _id: i });
      user.scores['likeRatioScore'] = 18;
      user.metrics['numActionsReceived'] = 35;
      await user.save();
  }

  user = await User.findOne({ _id: 0 });
  expect(user.metrics.carrotProfilesSeenByCarrotAlgoV2).to.equal(undefined);
  expect(user.metrics.carrotProfilesSeenByCarrotAlgoV1).to.equal(undefined);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles[0]._id).to.equal('45');
  expect(res.body.profiles[1]._id).to.equal('46');

  user = await User.findOne({ _id: 0 });
  expect(user.metrics.carrotProfilesSeenByCarrotAlgoV2).to.equal(1);
  expect(user.metrics.carrotProfilesSeenByCarrotAlgoV1).to.equal(undefined);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles[0]._id).to.equal('46');

  user = await User.findOne({ _id: 0 });
  expect(user.metrics.carrotProfilesSeenByCarrotAlgoV2).to.equal(2);
  expect(user.metrics.carrotProfilesSeenByCarrotAlgoV1).to.equal(undefined);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles[0]._id).to.equal('47');

  user = await User.findOne({ _id: 0 });
  expect(user.metrics.carrotProfilesSeenByCarrotAlgoV2).to.equal(3);
  expect(user.metrics.carrotProfilesSeenByCarrotAlgoV1).to.equal(undefined);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles[0]._id).to.equal('40');
  expect(res.body.profiles[1]._id).to.equal('41');

  user = await User.findOne({ _id: 0 });
  expect(user.metrics.carrotProfilesSeenByCarrotAlgoV2).to.equal(3);
  expect(user.metrics.carrotProfilesSeenByCarrotAlgoV1).to.equal(1);

});

it('fix carrot algo by reset topProfilesExclusionList after no souls found', async () => {
  const createMatchingUser = async (uid) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        minAge: 18,
        maxAge: 200,
        dating: ['male', 'female'],
        friends: ['male', 'female'],
        distance: null,
        distance2: null,
        local: true,
        global: true,
        showUsersOutsideMyRange: true,
        sameCountryOnly: false,
      });

      res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: 'female' });

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1
      });

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {}
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);
  };

  // Create multiple matching users with varied distances
  for (let uid = 0; uid < 5; uid++) {
    await createMatchingUser(uid);
    let user = await User.findOne({ _id: uid })
    user.scores.decayedScore = 0;
    user.scores.totalScore2 = 1;
    user.scores.totalScore = 10;
    user.scores.decayedScore2 = 0;
    await user.save();
  }

  await User.ensureIndexes();

  for (let i = 40; i < 45; i++) {
    await createMatchingUser(i);
      let user = await User.findOne({ _id: i });
      user.scores['totalScore2'] = 3;
      user.scores['decayedScore2'] = 3;
      await user.save();
  }

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.65' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);

  profiles = res.body.profiles;

  for (let i = 0; i < profiles.length; i++) {
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: profiles[i]._id,
      });
    expect(res.status).to.equal(200);
  }

    res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2)

    res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2)

    res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2)

    res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2)

    await new Promise((r) => setTimeout(r, 100));
    let userExclusion = await ExclusionList.findOne({ user: '0' })
    expect(userExclusion.topProfilesExclusionList.length).to.equal(4)

    res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1)

    await new Promise((r) => setTimeout(r, 100));
    userExclusion = await ExclusionList.findOne({ user: '0' })
    expect(userExclusion.topProfilesExclusionList.length).to.equal(5)

    res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2)

    await new Promise((r) => setTimeout(r, 100));
    userExclusion = await ExclusionList.findOne({ user: '0' })
    expect(userExclusion.topProfilesExclusionList.length).to.equal(1)

    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + ********;
    await user.save();

    res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(4)

    res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(4)
});

it('show 8 profiles in toppicks and gender balance the profiles', async () => {
  const createMatchingUser = async (uid, gender) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        minAge: 18,
        maxAge: 200,
        dating: ['male', 'female', 'non-binary'],
        friends: ['male', 'female', 'non-binary'],
        distance: null,
        distance2: null,
        local: true,
        global: true,
        showUsersOutsideMyRange: true,
        sameCountryOnly: false,
      });

      res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: gender });

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1
      });

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {}
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);
  };

  // Create multiple matching users with varied distances
  for (let uid = 0; uid < 10; uid++) {
    await createMatchingUser(uid, 'male');
    let user = await User.findOne({ _id: uid })
    await user.save();
  }
  for (let uid = 10; uid < 20; uid++) {
    await createMatchingUser(uid, 'female');
    let user = await User.findOne({ _id: uid })
    await user.save();
  }
  for (let uid = 20; uid < 30; uid++) {
    await createMatchingUser(uid, 'non-binary');
    let user = await User.findOne({ _id: uid })
    await user.save();
  }

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    dating: [],
    friends: ['male'],
  });

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(8);
  expect(res.body.alreadySuperLiked).to.eql([]);
  let genderCount = res.body.profiles.reduce((acc, x) => {
    acc[x.gender] = (acc[x.gender] || 0) + 1;
    return acc;
  }, {});
  expect(genderCount).to.eql({ male: 8 });

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    dating: [],
    friends: ['male', 'female'],
  });

  // tick 1 day
  clock = sinon.useFakeTimers({ now: Date.now() });
  clock.tick(1*24*60*60*1000);
  await TopPicksExclusionList.deleteMany({ })

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(8);
  expect(res.body.alreadySuperLiked).to.eql([]);
  genderCount = res.body.profiles.reduce((acc, x) => {
    acc[x.gender] = (acc[x.gender] || 0) + 1;
    return acc;
  }, {});
  expect(genderCount).to.eql({ male: 4, female: 4 });

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    dating: [],
    friends: ['male', 'female', 'non-binary'],
  });

  // tick 1 day
  clock.tick(1*24*60*60*1000);
  await TopPicksExclusionList.deleteMany({ })

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(8);
  expect(res.body.alreadySuperLiked).to.eql([]);
  genderCount = res.body.profiles.reduce((acc, x) => {
    acc[x.gender] = (acc[x.gender] || 0) + 1;
    return acc;
  }, {});
  expect(genderCount).to.eql({ male: 3, female: 3, 'non-binary': 2 });

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    dating: ['male'],
    friends: ['male', 'female', 'non-binary'],
  });

  // tick 1 day
  clock.tick(1*24*60*60*1000);
  await TopPicksExclusionList.deleteMany({ })

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(8);
  expect(res.body.alreadySuperLiked).to.eql([]);
  genderCount = res.body.profiles.reduce((acc, x) => {
    acc[x.gender] = (acc[x.gender] || 0) + 1;
    return acc;
  }, {});
  expect(genderCount).to.eql({ male: 8 });

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    dating: ['male', 'female'],
    friends: ['male', 'female', 'non-binary'],
  });

  // tick 1 day
  clock.tick(1*24*60*60*1000);
  await TopPicksExclusionList.deleteMany({ })

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(8);
  expect(res.body.alreadySuperLiked).to.eql([]);
  genderCount = res.body.profiles.reduce((acc, x) => {
    acc[x.gender] = (acc[x.gender] || 0) + 1;
    return acc;
  }, {});
  expect(genderCount).to.eql({ male: 4, female: 4 });

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    dating: ['male', 'female', 'non-binary'],
    friends: ['male', 'female', 'non-binary'],
  });

  // tick 1 day
  clock.tick(1*24*60*60*1000);
  await TopPicksExclusionList.deleteMany({ })

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(8);
  expect(res.body.alreadySuperLiked).to.eql([]);
  genderCount = res.body.profiles.reduce((acc, x) => {
    acc[x.gender] = (acc[x.gender] || 0) + 1;
    return acc;
  }, {});
  expect(genderCount).to.eql({ male: 3, female: 3, 'non-binary': 2 });

});

it('issue with duplicate profiles in top picks and new souls', async () => {
  const createMatchingUser = async (uid, gender) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        minAge: 18,
        maxAge: 200,
        dating: ['male', 'female', 'non-binary'],
        friends: ['male', 'female', 'non-binary'],
        distance: null,
        distance2: null,
        local: true,
        global: true,
        showUsersOutsideMyRange: true,
        sameCountryOnly: false,
      });

      res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: gender });

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1
      });

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {}
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);
  };

  // Create multiple matching users with varied distances
  for (let uid = 0; uid < 10; uid++) {
    await createMatchingUser(uid, 'male');
    let user = await User.findOne({ _id: uid })
    user['metrics'].numActionsReceived = 35;
    user['scores'].likeRatioScore = 19;
    if (user._id == '0') {
      user.pictures = []
      user.appVersion = '1.13.65';
    }
    await user.save();
  }

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    dating: [],
    friends: ['male'],
  });

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  let profileIds = res.body.profiles.map(x => x._id);
  expect(profileIds).to.eql(['1', '2']);

  let user = await User.findOne({ _id: 0 });
  expect(user.recentCarrotRecommendations).to.eql(['1', '2']);

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(7);
  profileIds = res.body.profiles.map(x => x._id);
  expect(profileIds).to.eql(['3', '4', '5', '6', '7', '8', '9']);
});

it('app_657 show only verified users on daily profiles', async () => {
  const createMatchingUser = async (uid) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        minAge: 18,
        maxAge: 200,
        dating: ['male', 'female'],
        friends: ['male', 'female'],
        distance: null,
        distance2: null,
        local: true,
        global: true,
        showUsersOutsideMyRange: true,
        sameCountryOnly: false,
      });

      res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: 'female' });

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1
      });

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {}
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);
  };

  for (let uid = 0; uid < 4; uid++) {
    await createMatchingUser(uid);
    let user = await User.findOne({ _id: uid })
    user.verification.status = 'unverified'
    if(uid != 3) user.createdAt = new Date('2022-09-24')
    user.scores['totalScore2'] = 3
    await user.save();
  }

  for (let uid = 4; uid < 10; uid++) {
    await createMatchingUser(uid);
    let user = await User.findOne({ _id: uid })
    user.verification.status = 'verified'
    if(uid != 4) user.createdAt = new Date('2022-09-24')
    await user.save();
  }

  await User.ensureIndexes();

  constants.hideUnverifiedUsers.restore();
  sinon.stub(constants, 'hideUnverifiedUsers').returns(true);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(DAILY_PROFILE_LIMIT);
  let resultProfilesId = res.body.profiles.map(x => x._id)
  expect(resultProfilesId).to.eql(['4','5','6','7']);
});

it('app_707 fix liked users filter with matching preferences', async () => {
  const createMatchingUser = async (uid, gender) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .send({ appVersion: '1.13.54' })
      .set('authorization', uid);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        minAge: 18,
        maxAge: 200,
        dating: ['male', 'female'],
        friends: ['male', 'female'],
        distance: null,
        distance2: null,
        local: true,
        global: true,
        showUsersOutsideMyRange: true,
        sameCountryOnly: false,
      });

      res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: gender });

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1
      });

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {}
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);
  };

  // Create multiple matching users with varied distances
  for (let uid = 0; uid < 8; uid++) {
    await createMatchingUser(uid, 'male');
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  let profileIds = res.body.profiles.map(x => x._id);
  expect(profileIds).to.eql(['1', '2', '3', '4']);


  for(let i = 3; i < 6; i++) {
    res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', i)
    .send({
      user: '0',
    });
    expect(res.status).to.equal(200);
  }

  let user = await UsersWhoLiked.findOne({ user: '0' })
  expect(user.usersWhoLiked).to.eql(['3', '4', '5']);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  profileIds = res.body.profiles.map(x => x._id);
  expect(profileIds).to.include.members(['1', '3', '4', '5']);

  // now for cached profiles for user who liked
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  profileIds = res.body.profiles.map(x => x._id);
  expect(profileIds).to.include.members(['1', '3', '4', '5']);

    await createMatchingUser(3, 'non-binary');
    await createMatchingUser(4, 'non-binary');

    // now for cached profiles for user who liked after changing gender
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  profileIds = res.body.profiles.map(x => x._id);
  expect(profileIds).to.include.members(['1', '2', '5', '6']);

});

it('fix showing already liked matched in daily profiles', async () => {
  const createMatchingUser = async (uid, gender) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .send({ appVersion: '1.13.76' })
      .set('authorization', uid);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        minAge: 18,
        maxAge: 200,
        dating: ['male', 'female', 'non-binary'],
        friends: ['male', 'female', 'non-binary'],
        distance: null,
        distance2: null,
        local: true,
        global: true,
        showUsersOutsideMyRange: true,
        sameCountryOnly: false,
      });

      res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: gender });

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1
      });

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {}
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);
  };

  // Create multiple matching users with varied distances
  for (let uid = 0; uid < 5; uid++) {
    await createMatchingUser(uid, 'male');
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  let profileIds = res.body.profiles.map(x => x._id);
  expect(profileIds).to.eql(['1', '2', '3', '4']);

  res = await request(app)
  .patch('/v1/user/sendLike')
  .set('authorization', 1)
  .send({
    user: '0',
  });
  expect(res.status).to.equal(200);

  let user1Exclusion = await ExclusionList.findOne({user: '1'});
  expect(user1Exclusion.exclusionList).to.eql(['0']);

  let user0Exclusion = await ExclusionList.findOne({user: '0'});
  expect(user0Exclusion.exclusionList).to.eql([]);



  res = await request(app)
  .patch('/v1/user/approve')
  .set('authorization', 0)
  .send({
    user: '1',
  });
  expect(res.status).to.equal(200);

  user1Exclusion = await ExclusionList.findOne({user: '1'});
  expect(user1Exclusion.exclusionList).to.eql(['0']);

  user0Exclusion = await ExclusionList.findOne({user: '0'});
  expect(user0Exclusion.exclusionList).to.eql(['1']);

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(3);
  profileIds = res.body.profiles.map(x => x._id);
  expect(profileIds).to.have.members(['2', '3', '4']);
});

it('show unspecified users', async () => {
  const createMatchingUser = async (uid, gender) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .send({ appVersion: '1.13.59' })
      .set('authorization', uid);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        minAge: 18,
        maxAge: 200,
        dating: ['male', 'female', 'non-binary'],
        friends: ['male', 'female', 'non-binary'],
        distance: null,
        distance2: null,
        local: true,
        global: true,
        showUsersOutsideMyRange: true,
        sameCountryOnly: false,
      });

      res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: gender });

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1
      });

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {}
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);
  };

  await createMatchingUser(0, 'male');
  await createMatchingUser(1, 'male');
  await createMatchingUser(2, 'male');
  await createMatchingUser(3, 'male');

  await User.updateOne({_id: '1'}, { $set: {
    enneagram: '1w9',
    relationshipStatus: 'Single',
    datingSubPreferences: 'Short term fun',
    sexuality: 'asexual',
    relationshipType: 'Monogamous',
    ethnicities: ['Bamar'],

    moreAboutUser: {
      exercise: 'Active',
      educationLevel: 'High school',
      drinking: 'Socially',
      smoking: 'Never',
      kids: 'Want someday',
      religion: 'Agnostic',
    },
   }})

  res = await request(app)
  .get('/v1/user')
  .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.enneagram).to.equal('1w9');
  expect(res.body.relationshipStatus).to.equal('Single');
  expect(res.body.datingSubPreferences).to.equal('Short term fun');
  expect(res.body.sexuality).to.equal('asexual');
  expect(res.body.relationshipType).to.equal('Monogamous');
  expect(res.body.ethnicities).to.eql(['Bamar']);
  expect(res.body.moreAboutUser.exercise).to.eql('Active');
  expect(res.body.moreAboutUser.educationLevel).to.eql('High school');
  expect(res.body.moreAboutUser.drinking).to.eql('Socially');
  expect(res.body.moreAboutUser.smoking).to.eql('Never');
  expect(res.body.moreAboutUser.kids).to.eql('Want someday');
  expect(res.body.moreAboutUser.religion).to.eql('Agnostic');

  await User.updateOne({_id: '2'}, { $set: {
    enneagram: '1w9',
    moreAboutUser: {
      exercise: 'Active',
    },
   }})

  res = await request(app)
  .get('/v1/user')
  .set('authorization', 2)
  expect(res.status).to.equal(200);
  expect(res.body.enneagram).to.equal('1w9');
  expect(res.body.moreAboutUser.exercise).to.eql('Active');

  expect(res.body.ethnicities).to.eql();
  expect(res.body.relationshipStatus).to.equal();
  expect(res.body.datingSubPreferences).to.equal();
  expect(res.body.sexuality).to.equal();
  expect(res.body.relationshipType).to.equal();
  expect(res.body.moreAboutUser.educationLevel).to.eql();
  expect(res.body.moreAboutUser.drinking).to.eql();
  expect(res.body.moreAboutUser.smoking).to.eql();
  expect(res.body.moreAboutUser.kids).to.eql();
  expect(res.body.moreAboutUser.religion).to.eql();

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    minAge: 18,
    maxAge: 200,
    dating: ['male', 'female', 'non-binary'],
    friends: ['male', 'female', 'non-binary'],
    distance: null,
    distance2: null,
    local: true,
    global: true,
    showUsersOutsideMyRange: true,
    sameCountryOnly: false,
    enneagrams: ['1w9'],
    relationshipStatus: ['Single'],
    datingSubPreferences: ['Short term fun'],
    sexuality: ['asexual'],
    relationshipType: ['Monogamous'],
    ethnicities: ['Bamar'],
    exercise: ['Active'],
    educationLevel: ['High school'],
    drinking: ['Socially'],
    smoking: ['Never'],
    kids: ['Want someday'],
    religion: ['Agnostic'],
  });

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  let profileIds = res.body.profiles.map(x => x._id);
  expect(profileIds).to.eql(['1']);

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    minAge: 18,
    maxAge: 200,
    dating: ['male', 'female', 'non-binary'],
    friends: ['male', 'female', 'non-binary'],
    distance: null,
    distance2: null,
    local: true,
    global: true,
    showUsersOutsideMyRange: true,
    sameCountryOnly: false,
    enneagrams: ['1w9'],
    relationshipStatus: ['Single'],
    datingSubPreferences: ['Short term fun'],
    sexuality: ['asexual'],
    relationshipType: ['Monogamous'],
    ethnicities: ['Bamar'],
    exercise: ['Active'],
    educationLevel: ['High school'],
    drinking: ['Socially'],
    smoking: ['Never'],
    kids: ['Want someday'],
    religion: ['Agnostic'],
    showUnspecified: {
      enneagrams: false,
      exercise: false,
      //
      relationshipStatus: true,
      datingSubPreferences: true,
      sexuality: true,
      relationshipType: true,
      ethnicities: true,
      educationLevel: true,
      drinking: true,
      smoking: true,
      kids: true,
      religion: true,
    }
  });

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  profileIds = res.body.profiles.map(x => x._id);
  expect(profileIds).to.eql(['1', '2']);

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    minAge: 18,
    maxAge: 200,
    dating: ['male', 'female', 'non-binary'],
    friends: ['male', 'female', 'non-binary'],
    distance: null,
    distance2: null,
    local: true,
    global: true,
    showUsersOutsideMyRange: true,
    sameCountryOnly: false,
    enneagrams: ['1w9'],
    relationshipStatus: ['Single'],
    datingSubPreferences: ['Short term fun'],
    sexuality: ['asexual'],
    relationshipType: ['Monogamous'],
    ethnicities: ['Bamar'],
    exercise: ['Active'],
    educationLevel: ['High school'],
    drinking: ['Socially'],
    smoking: ['Never'],
    kids: ['Want someday'],
    religion: ['Agnostic'],
    showUnspecified: {
      enneagrams: true,
      exercise: true,
      relationshipStatus: true,
      datingSubPreferences: true,
      relationshipType: true,
      sexuality: true,
      ethnicities: true,
      educationLevel: true,
      drinking: true,
      smoking: true,
      kids: true,
      religion: true,
    }
  });

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(3);
  profileIds = res.body.profiles.map(x => x._id);
  expect(profileIds).to.eql(['1', '2', '3']);

});

it('profile filter with negative preferences for interests', async () => {
  await Interest.create({ category: 'gaming', interest: '#gaming', name: 'gaming', sortIndex: 4 })

  const createMatchingUser = async (uid, gender) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .send({ appVersion: '1.13.54' })
      .set('authorization', uid);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        minAge: 18,
        maxAge: 200,
        dating: ['male', 'female', 'non-binary'],
        friends: ['male', 'female', 'non-binary'],
        distance: null,
        distance2: null,
        local: true,
        global: true,
        showUsersOutsideMyRange: true,
        sameCountryOnly: false,
      });
      expect(res.status).to.equal(200);

      res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: gender });

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1
      });

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {}
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);
  };

  // Create multiple matching users with varied distances
  for (let uid = 0; uid < 5; uid++) {
    await createMatchingUser(uid, 'male');
  }

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  expect(res.body.profiles.map((x) => x._id)).to.have.same.members(['1','2','3','4']);

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    interestNames: ['kpop', 'chess'],
    excludedInterestNames: ['kpop'],
  });
  expect(res.status).to.equal(422); // same values for interestNames and excludedInterestNames not accepted

  for (let uid = 0; uid < 3; uid++) {
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', uid)
      .send({
        interestNames: ['chess', 'gaming'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .get('/v1/user')
    .set('authorization', uid)
    expect(res.status).to.equal(200);
    expect(res.body.interestNames).to.eql(['chess', 'gaming']);
    expect(res.body.preferences.interestNames).to.eql();
    expect(res.body.preferences.excludedInterestNames).to.eql();
  }

  for (let uid = 3; uid < 5; uid++) {
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', uid)
      .send({
        interestNames: ['latin'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .get('/v1/user')
    .set('authorization', uid)
    expect(res.status).to.equal(200);
    expect(res.body.interestNames).to.eql(['latin']);
    expect(res.body.preferences.interestNames).to.eql();
    expect(res.body.preferences.excludedInterestNames).to.eql();
  }

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  expect(res.body.profiles.map((x) => x._id)).to.have.same.members(['1','2','3','4']);


  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    interestNames: ['kpop'],
    excludedInterestNames: null,
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .get('/v1/user')
  .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.interestNames).to.eql(['chess', 'gaming']);
  expect(res.body.preferences.interestNames).to.eql(['kpop']);
  expect(res.body.preferences.excludedInterestNames).to.eql();

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    interestNames: ['latin', 'chess'],
    excludedInterestNames: null,
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .get('/v1/user')
  .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.interestNames).to.eql(['chess', 'gaming']);
  expect(res.body.preferences.interestNames).to.eql(['latin', 'chess']);
  expect(res.body.preferences.excludedInterestNames).to.eql();

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  expect(res.body.profiles.map((x) => x._id)).to.have.same.members(['1', '2', '3', '4']);

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    interestNames: ['latin'],
    excludedInterestNames: ['chess'],
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .get('/v1/user')
  .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.interestNames).to.eql(['chess', 'gaming']);
  expect(res.body.preferences.interestNames).to.eql(['latin']);
  expect(res.body.preferences.excludedInterestNames).to.eql(['chess']);

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles.map((x) => x._id)).to.have.same.members(['3','4']); // 3,4 have interest as latin

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    interestNames: ['chess', 'kpop'],
    excludedInterestNames: ['gaming', 'latin'],
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .get('/v1/user')
  .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.interestNames).to.eql(['chess', 'gaming']);
  expect(res.body.preferences.interestNames).to.eql(['chess', 'kpop']);
  expect(res.body.preferences.excludedInterestNames).to.eql(['gaming', 'latin']);

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 0)
  .send({
    interestNames: null,
    excludedInterestNames: ['latin'],
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .get('/v1/user')
  .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.interestNames).to.eql(['chess', 'gaming']);
  expect(res.body.preferences.interestNames).to.eql();
  expect(res.body.preferences.excludedInterestNames).to.eql(['latin']);

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles.map((x) => x._id)).to.have.same.members(['1','2'])

});

it('dont show hideHoroscope profiles when horoscopes filter is true', async () => {

  for (let uid = 0; uid < 2; uid++) {
    await createMatchingUserGeneral(uid, 'male');
  }

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('0');

  res = await request(app)
  .patch('/v1/user/preferences')
  .set('authorization', 1)
  .send({
    horoscopes: ['Virgo'],
  });
  expect(res.status).to.equal(200);

  let user = await User.findOne({ _id: '1' })
  expect(user.preferences.horoscopes).to.eql(['Virgo']);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('0');

  // hide horoscope
  res = await request(app)
    .put('/v1/user/hideHoroscope')
    .set('authorization', 0)
    .send({
      hideHoroscope: true,
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: '0' })
  expect(user.hideHoroscope).to.equal(true);

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);
});

it('issue with params update in internal loop function', async function() {

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.48' });
  expect(res.status).to.equal(200);

  const numUsers = 4;
  for (let uid = 0; uid < numUsers; uid++) {
    await initUser(uid);
    if(uid == 2){ // making id 2 as top profile
      user = await User.findById('2');
      user['scores'].decayedScore2 = 3
      user['scores'].totalScore2 = 2
      await user.save()
    }
    if(uid == 3){ // making id 3 as low profile
      user = await User.findById('3');
      user['scores'].decayedScore2 = 0
      user['scores'].totalScore2 = 0
      user.age = 25
      user.decayedScore2 = 0
      await user.save()
    }
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(3);
  expect(res.body.profiles.map(x => x._id)).to.have.same.members(['1','2','3']);

  await new Promise((resolve) => setTimeout(resolve, 1000)); //making sure pre fetch is complete

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.recentRecommendations).to.have.same.members(['1','2','3']);

  // profiles should load from recentRecommendations for id 1,2,3 as theres no data changes occuring
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.profiles.map((x) => x._id)).to.have.same.members(['1','2','3']);
  expect(res.body.profiles.length).to.equal(3); // id 1 and 3 are only returning
});

it('discover users app_749', async () => {
  let clock

  const createMatchingUser = async (uid) => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .send({ appVersion: '1.13.59' })
      .set('authorization', uid);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        minAge: 18,
        maxAge: 200,
        dating: ['female'],
        friends: ['male'],
        distance: null,
        distance2: null,
        local: true,
        global: true,
        showUsersOutsideMyRange: true,
        sameCountryOnly: false,
      });

      res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: 'male' });

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', uid)
      .send({
        year: 1990,
        month: 1,
        day: 1
      });

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', uid)
      .send({
        answers: {}
      });

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({ latitude: 33.1586, longitude: -116.6606 });
    expect(res.status).to.equal(200);

    let user = await User.findOne({ _id: uid });
    user.profileModifiedAt = new Date()
    await user.save();
  };

  for (let uid = 0; uid < 9; uid++) {
    await createMatchingUser(uid);
    if (uid < 10) {
      let user = await User.findOne({ _id: uid })
      user.scores['totalScore2'] = 3;
      user.scores['decayedScore2'] = 3;
      user.metrics['numActionsReceived'] = 35;
      user.scores['likeRatioScore'] = 19;
      await user.save();
    }
  }

  for (let uid = 10; uid < 14; uid++) {
    await createMatchingUser(uid);
    res = await request(app)
      .put('/v1/user/datingSubPreferences')
      .set('authorization', uid)
      .send({
        datingSubPreferences: 'Short term fun',
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/datingSubPreferences')
    .set('authorization', 0)
    .send({
      datingSubPreferences: 'Short term fun',
    });
  expect(res.status).to.equal(200);


  for (let uid = 14; uid < 18; uid++) {
    await createMatchingUser(uid);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', uid)
      .send({
        interestNames: ['kpop'],
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({
      interestNames: ['kpop'],
    });
  expect(res.status).to.equal(200);

  // not matching gender with dating
  res = await request(app)
    .get('/v1/user/discover')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.topPicksDating.length).to.equal(0);
  expect(res.body.topPicksInterests.length).to.equal(4);
  expect(res.body.topPicksInterests.map(x => x._id)).to.have.same.members(['14','15','16','17']);
  expect(res.body.topPicks.length).to.equal(8);
  expect(res.body.topPicks.map(x => x._id)).to.have.same.members(['1','2','3','4','5','6','7','8']);

  // change preferences of 10,11,12,13 so that matches with users looking for preferences
  for (let uid = 10; uid < 14; uid++) {

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', uid)
      .send({
        minAge: 18,
        maxAge: 200,
        dating: ['male'],
        friends: ['male'],
        distance: null,
        distance2: null,
        local: true,
        global: true,
        showUsersOutsideMyRange: true,
        sameCountryOnly: false,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', uid)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);
  }

  user = await User.findOne({ _id: 0 });
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYou).to.equal(1);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYou).to.equal(1);

  res = await request(app)
    .get('/v1/user/discover')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.topPicksDating.length).to.equal(4);
  expect(res.body.topPicksDating.map(x => x._id)).to.have.same.members(['10','11','12','13']);
  expect(res.body.topPicksDating[0].datingLabel).to.eql(['Short term fun']);
  expect(res.body.topPicksDating[0].datingSubPreferences).to.equal('Short term fun');
  expect(res.body.topPicksInterests.length).to.equal(4);
  expect(res.body.topPicksInterests.map(x => x._id)).to.have.same.members(['14','15','16','17']);
  expect(res.body.topPicks.length).to.equal(8);
  expect(res.body.topPicks.map(x => x._id)).to.have.same.members(['1','2','3','4','5','6','7','8']);

  user = await User.findOne({ _id: 0 });
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYou).to.equal(1);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYou).to.equal(1);
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(0);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(0);
  expect(user.currentDayMetrics.topPicksSimilarInterests).to.have.same.members(['14','15','16','17']);
  expect(user.currentDayMetrics.topPicksSameDatingGoals).to.have.same.members(['10','11','12','13']);
  expect(user.currentDayMetrics.topPicks).to.have.same.members(['1','2','3','4','5','6','7','8']);

  res = await request(app)
    .patch('/v1/user/block')
    .set('authorization', 0)
    .send({ user: '17' });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.currentDayMetrics.topPicksSimilarInterests).to.have.same.members(['14','15','16']);

  await createMatchingUser(19);
  await createMatchingUser(20);

  // checking that toppicks are excluded from daily profiles
  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2)
  expect(res.body.profiles.map(x => x._id)).to.have.same.members(['19', '20']);

  user = await User.findOne({ _id: 0 });
  user.numSuperLikes = 5;
  await user.save();

  res = await request(app)
    .patch('/v1/user/sendSuperLike')
    .set('authorization', 0)
    .send({
      user: '14',
      message: 'Hi',
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.currentDayMetrics.topPicksSuperLiked).to.have.same.members(['14']);

  res = await request(app)
    .patch('/v1/user/sendSuperLike')
    .set('authorization', 0)
    .send({
      user: '15',
      message: 'Hi',
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.currentDayMetrics.topPicksSuperLiked).to.have.same.members(['14','15']);

  res = await request(app)
    .patch('/v1/user/sendSuperLike')
    .set('authorization', 0)
    .send({
      user: '2',
      message: 'Hi',
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYou).to.equal(1);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYou).to.equal(1);
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(0);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(0);
  expect(user.currentDayMetrics.topPicksSuperLiked).to.have.same.members(['14','15','2']);

  clock = sinon.useFakeTimers({ now: Date.now() });
  clock.tick(1*24*60*60*1000);

  await TopPicksExclusionList.updateOne({ user: '0' }, { $pull: { 'priorTopPicksSameDatingGoals': '11' }});

  res = await request(app)
    .put('/v1/user/initApp')
    .send({ appVersion: '1.13.59' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYou).to.equal(0);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYou).to.equal(1);
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(0);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(0);

  res = await request(app)
    .get('/v1/user/discover')
    .set('authorization',0)
  expect(res.status).to.equal(200);
  expect(res.body.topPicksDating.length).to.equal(1);
  expect(res.body.topPicksDating.map(x => x._id)).to.have.same.members(['11']);
  expect(res.body.topPicksInterests.length).to.equal(1);
  expect(res.body.topPicksInterests.map(x => x._id)).to.have.same.members(['16']);
  expect(res.body.topPicks.length).to.equal(8);
  expect(res.body.topPicks.map(x => x._id)).to.have.same.members(['1','3','4','5','6','10','12','13']);

  user = await User.findOne({ _id: 0 });
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYou).to.equal(1);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYou).to.equal(2);
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(1);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(1);
  expect(user.currentDayMetrics.topPicksSimilarInterests).to.have.same.members(['16']);
  expect(user.currentDayMetrics.topPicksSameDatingGoals).to.have.same.members(['11']);
  expect(user.currentDayMetrics.topPicks).to.have.same.members(['1','3','4','5','6','10','12','13']);

  res = await request(app)
    .get('/v1/user/discover')
    .set('authorization',0)
  expect(res.status).to.equal(200);
  expect(res.body.topPicksDating.length).to.equal(1);
  expect(res.body.topPicksDating.map(x => x._id)).to.have.same.members(['11']);
  expect(res.body.topPicksDating[0].preferences.purpose).to.have.same.members(['dating']);
  expect(res.body.topPicksDating[0].datingLabel).to.eql(['Short term fun']);

  user = await User.findOne({ _id: '0' })
  user.datingSubPreferences = undefined
  await user.save()

  res = await request(app)
    .get('/v1/user/discover')
    .set('authorization',0)
  expect(res.status).to.equal(200);
  expect(res.body.topPicksDating.length).to.equal(0);

  res = await request(app)
  .put('/v1/user/datingSubPreferences')
  .set('authorization', 0)
  .send({
    datingSubPreferences: 'Short term fun',
  });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/discover')
    .set('authorization',0)
  expect(res.status).to.equal(200);
  expect(res.body.topPicksDating.length).to.equal(1);
  expect(res.body.topPicksDating.map(x => x._id)).to.have.same.members(['11']);
  expect(res.body.topPicksDating[0].datingLabel).to.eql(['Short term fun']);

  clock.tick(1*24*60*60*1000);

  res = await request(app)
    .get('/v1/user/discover')
    .set('authorization',0)
  expect(res.status).to.equal(200);
  expect(res.body.topPicksDating.length).to.equal(4);
  expect(res.body.topPicksDating.map(x => x._id)).to.have.same.members(['10','11','12','13']);
  expect(res.body.topPicksInterests.length).to.equal(1);
  expect(res.body.topPicksInterests.map(x => x._id)).to.have.same.members(['16']);
  expect(res.body.topPicks.length).to.equal(7);
  expect(res.body.topPicks.map(x => x._id)).to.have.same.members([ '1','3','4','5','6','7','8']);

  clock.tick(1*24*60*60*1000);

  res = await request(app) // this resets the recommendationsExhaustedAt dates
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      minAge: 19,
      maxAge: 19,
      friends: ['male', 'female', 'non-binary'],
      dating: [],
      distance: null,
      distance2: null,
      local: true,
      global: true,
      showUsersOutsideMyRange: true,
      sameCountryOnly: false,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/discover')
    .set('authorization',0)
  expect(res.status).to.equal(200);
  expect(res.body.topPicksDating).to.equal();

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(8);
  expect(res.body.profiles.map(x => x._id)).to.have.same.members(['2','3','4','5','10','11','12','13']);

  user = await User.findOne({ _id: '1' }).lean()
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYou).to.equal(0);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYou).to.equal(0);
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(0);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(0);

  clock.tick(1*24*60*60*1000);

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(8);
  expect(res.body.profiles.map(x => x._id)).to.have.same.members(['6','7','8','14','15','16','17','19']);

  user = await User.findOne({ _id: '1' }).lean()
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYou).to.equal(0);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYou).to.equal(0);
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(0);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(0);

  clock.tick(1*24*60*60*1000);

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles.map(x => x._id)).to.have.same.members(['20']);

  clock.tick(1*24*60*60*1000);

  user = await User.findOne({ _id: '1' }).lean()
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYou).to.equal(0);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYou).to.equal(0);
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(0);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(0);

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(8);
  expect(res.body.profiles.map(x => x._id)).to.have.same.members(['2','3','4','5','10','11','12','13']);

  user = await User.findOne({ _id: '1' }).lean()
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYou).to.equal(1);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYou).to.equal(1);
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(1);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(1);

  clock.tick(1*24*60*60*1000);

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(8);
  expect(res.body.profiles.map(x => x._id)).to.have.same.members(['2','3','4','5','10','11','12','13']);

  user = await User.findOne({ _id: '1' })
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYou).to.equal(1);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYou).to.equal(2);
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(1);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(2);

  user.currentDayMetrics.topPicks = []
  await user.save()

  user = await User.findOne({ _id: '1' })
  expect(user.currentDayMetrics.topPicks).to.eql([]);

  res = await request(app)
    .get('/v1/user/topPicks')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(8);
  expect(res.body.profiles.map(x => x._id)).to.have.same.members(['2','3','4','5','10','11','12','13']);

  user = await User.findOne({ _id: '1' })
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYou).to.equal(1);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYou).to.equal(2);
  expect(user.currentDayMetrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(1);
  expect(user.metrics.numDaysRecycledProfilesUsedInForYouFound).to.equal(2);

  clock.restore();
})

/*
it('hideFromNearby to use acutalLocation instead of location', async () => {

  for (let uid = 0; uid < 5; uid++) {
    await createMatchingUserGeneral(uid, 'male', true);

    if(uid == 4){
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          longitude: 86.2818305,
          latitude: 26.8135563,
        });
      expect(res.status).to.equal(200);
    }else{
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          longitude: 85,
          latitude: 27,
        });
      expect(res.status).to.equal(200);
    }

    let user = await User.findOne({ _id: uid });
    user.premiumExpiration = Date.now() + ********;
    await user.save();
    expect(user.actualLongitude).to.equal(user.longitude2);
    expect(user.actualLatitude).to.equal(user.latitude2);
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  expect(res.body.profiles.map((x)=>x._id)).to.have.same.members(['1','2','3','4']);

  let user = await User.findOne({ _id: 0 })
  user.recentRecommendations = []
  await user.save()

  // telaport location to near location within 10miles range for user 4 and enabling hideFromNearBy

  res = await request(app)
    .put(`/v1/user/hideFromNearby`)
    .set('authorization', 4)
    .send({
      hideFromNearby: true,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/teleport/location')
    .set('authorization', 4)
    .send({
      longitude: 85,
      latitude: 27,
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 4 })
  expect(user.hideFromNearby).to.equal(true);
  expect(user.location.coordinates).to.eql([85, 27]);
  expect(user.actualLocation.coordinates).to.eql([86.2818305, 26.8135563]);
  expect(user.actualLongitude).to.not.equal(user.longitude2);
  expect(user.actualLatitude).to.not.equal(user.latitude2);

  // user 4 should still visible because actual location is more than 10miles range
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  expect(res.body.profiles.map((x)=>x._id)).to.have.same.members(['1','2','3','4']);

  user = await User.findOne({ _id: 0 })
  user.recentRecommendations = []
  await user.save()

  // telaport location to far location outside 10miles range for user 3 and enabling hideFromNearBy

  res = await request(app)
    .put(`/v1/user/hideFromNearby`)
    .set('authorization', 3)
    .send({
      hideFromNearby: true,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/teleport/location')
    .set('authorization', 3)
    .send({
      longitude: 86.2818305,
      latitude: 26.8135563,
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 3 })
  expect(user.hideFromNearby).to.equal(true);
  expect(user.actualLocation.coordinates).to.eql([85, 27]);
  expect(user.location.coordinates).to.eql([86.2818305, 26.8135563]);
  expect(user.actualLongitude).to.not.equal(user.longitude2);
  expect(user.actualLatitude).to.not.equal(user.latitude2);

  // user 3 should not be visible because actual location is within than 10miles range

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(3);
  expect(res.body.profiles.map((x)=>x._id)).to.have.same.members(['1','2','4']);

  user = await User.findOne({ _id: 0 })
  user.recentRecommendations = []
  await user.save()

  // for user 0 enabling hideFromNearBy

  res = await request(app)
    .put(`/v1/user/hideFromNearby`)
    .set('authorization', 0)
    .send({
      hideFromNearby: true,
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 })
  expect(user.hideFromNearby).to.equal(true);
  expect(user.actualLocation.coordinates).to.eql([85, 27]);

  // user 1,2,3 should not be visible because actual location is within than 10miles range of user 0's actual location

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles.map((x)=>x._id)).to.have.same.members(['4']);

  user = await User.findOne({ _id: 0 })
  user.recentRecommendations = []
  await user.save()

  // for user 0 using teleport

  res = await request(app)
    .put('/v1/teleport/location')
    .set('authorization', 0)
    .send({
      longitude: 86.2818305,
      latitude: 26.8135563,
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 })
  expect(user.hideFromNearby).to.equal(true);
  expect(user.actualLocation.coordinates).to.eql([85, 27]);
  expect(user.location.coordinates).to.eql([86.2818305, 26.8135563]);
  expect(user.actualLongitude).to.not.equal(user.longitude2);
  expect(user.actualLatitude).to.not.equal(user.latitude2);

  // user 1,2,3 should not be visible because actual location is within than 10miles range of user 0's actual location
  // but 4 should be still visible because its actual location is far from user 0's actual location
  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles.map((x)=>x._id)).to.have.same.members(['4']);

});
*/

it('age preference both minAge and maxAge set to 18', async () => {
  for (let uid = 0; uid < 4; uid++) {
    await createMatchingUserGeneral(uid, 'male');
    if (uid < 2) {
      const today = new Date();
      const birthDate = new Date(
        today.getFullYear() - 18,
        today.getMonth(),
        today.getDate()
      );
      let res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: birthDate.getFullYear(),
          month: birthDate.getMonth() + 1,
          day: birthDate.getDate()
        });

      expect(res.status).to.equal(200);
    }

  }

  let res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(3);

  let user = await User.findOne({ _id: '1' })
  user.age = 18
  await user.save()

  user = await User.findOne({ _id: '2' })
  user.age = 19
  await user.save()

  user = await User.findOne({ _id: '3' })
  user.age = 25
  await user.save()

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(3);

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      minAge: 19,
      maxAge: 19,
      dating: ['male', 'female', 'non-binary'],
      friends: ['male', 'female', 'non-binary'],
      distance: null,
      distance2: null,
      local: true,
      global: true,
      showUsersOutsideMyRange: true,
      sameCountryOnly: false,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('2');

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      minAge: 18,
      maxAge: 18,
      dating: ['male', 'female', 'non-binary'],
      friends: ['male', 'female', 'non-binary'],
      distance: null,
      distance2: null,
      local: true,
      global: true,
      showUsersOutsideMyRange: true,
      sameCountryOnly: false,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0]._id).to.equal('1');
});

it('fix boosting profiles visibility', async () => {
  let clock = sinon.useFakeTimers({
    now: Date.now(),
    toFake: ['Date'], // Only fake Date, not setTimeout
  });

  for (let uid = 0; uid < 6; uid++) {
    await createMatchingUserGeneral(uid, 'male');
    let user = await User.findOne({ _id: uid })
    user.scores.decayedScore2 = 1;
    user.scores.totalScore = 1
    if(uid == 5) user.scores.decayedScore2 = 0
    if(uid < 4) user.scores.totalScore = 3
    await user.save()
  }

  // move ahead 1 days
  clock.tick(1*24*60*60*1000);

  let res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  expect(res.body.profiles.map(x => x._id)).to.have.same.members(['1','2','3','4']);

  await new Promise((resolve) => setTimeout(resolve, 500));

  let user = await User.findOne({ _id: 0 })
  user.recentRecommendations = []
  await user.save()

  let preBoostDate = user.profileModifiedAt

  // move ahead 1 days
  clock.tick(1*24*60*60*1000);

  let userMetadata = await UserMetadata.findOne({ user: 5 })
  userMetadata.coins = 10000
  await userMetadata.save()

  user = await User.findOne({ _id: 5 }).lean()
  expect(user.scores.decayedScore2).to.equal(0);

  // call boost for user 5
  res = await request(app)
    .put('/v1/coins/boost')
    .set('authorization', 5)
    .send({ price: 200 });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 5 }).lean()
  expect(user.scores.decayedScore2).to.equal(3);
  expect(user.profileModifiedAt).to.be.greaterThan(preBoostDate);

  await new Promise((resolve) => setTimeout(resolve, 500));

  res = await request(app)
  .get('/v1/user/dailyProfiles')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  expect(res.body.profiles.map(x => x._id)).to.have.same.members(['1','2','3','5']);

  clock.restore()
});

it('prioritize active profiles before distance app_924', async () => {
  let clock = sinon.useFakeTimers({
    now: Date.now(),
    toFake: ['Date'],
  });

  for (let uid = 0; uid < 7; uid++) {
    if(uid === 0 || uid === 1){
      await createMatchingUserGeneral(uid, 'female');
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          dating: ['male'],
        });
      expect(res.status).to.equal(200);
    }else{
      await createMatchingUserGeneral(uid, 'male');
    }

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({
        longitude: 85,
        latitude: 27,
      });
    expect(res.status).to.equal(200);

    let user = await User.findOne({ _id: uid });
    user.scores.decayedScore2 = 0;
    await user.save();
  }

  clock.tick(8*24*60*60*1000);

  for (let uid = 7; uid < 14; uid++) {
    await createMatchingUserGeneral(uid, 'male');
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({
        longitude: 86.2818305,
        latitude: 26.8135563,
      });
    expect(res.status).to.equal(200);

    if(uid < 10){
      let user = await User.findOne({ _id: uid });
      user.scores.decayedScore2 = 2;
      await user.save();
    }else{
      let user = await User.findOne({ _id: uid });
      user.scores.decayedScore2 = 1;
      await user.save();
    }
  }

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  expect(res.body.profiles.map(x => x._id)).to.have.same.members(['7', '8', '9', '10']);

  user = await User.findOne({ _id: 0 }).lean()
  expect(user.metrics.numOfActiveMalesViewed).to.equal(0);
  expect(user.metrics.numOfActiveMalesLiked).to.equal(0);
  expect(user.metrics.numOfInactiveMalesViewed).to.equal(0);
  expect(user.metrics.numOfInactiveMalesLiked).to.equal(0);

  user = await User.findOne({ _id: 1 }).lean()
  expect(user.metrics.numOfActiveMalesViewed).to.equal(0);
  expect(user.metrics.numOfActiveMalesLiked).to.equal(0);
  expect(user.metrics.numOfInactiveMalesViewed).to.equal(0);
  expect(user.metrics.numOfInactiveMalesLiked).to.equal(0);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '7',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: '7',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '7',
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 }).lean()
  expect(user.metrics.numOfActiveMalesViewed).to.equal(1);
  expect(user.metrics.numOfActiveMalesLiked).to.equal(1);
  expect(user.metrics.numOfInactiveMalesViewed).to.equal(0);
  expect(user.metrics.numOfInactiveMalesLiked).to.equal(0);

  user = await User.findOne({ _id: 1 }).lean()
  expect(user.metrics.numOfActiveMalesViewed).to.equal(1);
  expect(user.metrics.numOfActiveMalesLiked).to.equal(1);
  expect(user.metrics.numOfInactiveMalesViewed).to.equal(0);
  expect(user.metrics.numOfInactiveMalesLiked).to.equal(0);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '3',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: '3',
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 }).lean()
  expect(user.metrics.numOfActiveMalesViewed).to.equal(1);
  expect(user.metrics.numOfActiveMalesLiked).to.equal(1);
  expect(user.metrics.numOfInactiveMalesViewed).to.equal(1);
  expect(user.metrics.numOfInactiveMalesLiked).to.equal(1);

  user = await User.findOne({ _id: 1 }).lean()
  expect(user.metrics.numOfActiveMalesViewed).to.equal(1);
  expect(user.metrics.numOfActiveMalesLiked).to.equal(1);
  expect(user.metrics.numOfInactiveMalesViewed).to.equal(1);
  expect(user.metrics.numOfInactiveMalesLiked).to.equal(1);

  user = await User.findOne({ _id: 0 })
  user.premiumExpiration = new Date(Date.now() + 365 * 24 * 3600 * 1000);
  await user.save();

  for(let i = 8; i < 13; i++){
    // pass all profiles
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: `${i}`,
      });
    expect(res.status).to.equal(200);
  }

  user = await User.findOne({ _id: 0 }).lean()
  expect(user.metrics.numOfActiveMalesViewed).to.equal(6);
  expect(user.metrics.numOfActiveMalesLiked).to.equal(1);
  expect(user.metrics.numOfInactiveMalesViewed).to.equal(1);
  expect(user.metrics.numOfInactiveMalesLiked).to.equal(1);

  // call /profileDetails for 13

  res = await request(app)
    .get('/v1/user/profileDetails')
    .set('authorization', 0)
    .query({ user: '13' });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 }).lean()
  expect(user.metrics.numOfActiveMalesViewed).to.equal(7);
  expect(user.metrics.numOfActiveMalesLiked).to.equal(1);
  expect(user.metrics.numOfInactiveMalesViewed).to.equal(1);
  expect(user.metrics.numOfInactiveMalesLiked).to.equal(1);

  res = await request(app)
    .get('/v1/user/profileDetails')
    .set('authorization', 0)
    .query({ user: '13' });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 }).lean()
  expect(user.metrics.numOfActiveMalesViewed).to.equal(7);
  expect(user.metrics.numOfActiveMalesLiked).to.equal(1);
  expect(user.metrics.numOfInactiveMalesViewed).to.equal(1);
  expect(user.metrics.numOfInactiveMalesLiked).to.equal(1);

  res = await request(app)
    .get('/v1/user/dailyProfiles')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(4);
  expect(res.body.profiles.map(x => x._id)).to.have.same.members(['13', '1', '2', '4']);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 200)
    .send({ appVersion: '1.13.99' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/gender')
    .set('authorization', 200)
    .send({ gender: 'female' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 200)
    .send({
      dating: ['male'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 201)
    .send({ appVersion: '1.13.97' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/gender')
    .set('authorization', 201)
    .send({ gender: 'female' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 201)
    .send({
      dating: ['male'],
    });
  expect(res.status).to.equal(200);

  clock.restore()
})

describe('BSON size error handling', () => {
  afterEach(() => {
    sinon.restore(); // Restore stubs to prevent side effects
  });

  it('should return HTTP 429 when BSON size error occurs and create recalculation record', async () => {
    for(let i=0; i<4; i++){
      await createMatchingUserGeneral(i);
    }

    const bsonSizeError = new mongoose.mongo.MongoServerError({
      message: "BSONObj size",
      code: 103, // BSON size error code
    });

    let aggregateStub = sinon.stub(User, 'aggregate').returns({
      read: sinon.stub().throws(bsonSizeError)
    });

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);

    expect(aggregateStub.calledOnce).to.be.false;

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(429);
    expect(res.text).to.equal('Something went wrong. Please try again.');

    expect(aggregateStub.calledOnce).to.be.true;

    const updatedUser = await User.findById('0');
    expect(updatedUser.exclusionListFailed).to.equal(false);
    expect(updatedUser.sizeOfExclusionUsedDuringFailure).to.equal(2);

    const recalculationRecord = await ExclusionListRecalculation.findOne({ user: '0' });
    expect(recalculationRecord).to.not.be.null;
    expect(recalculationRecord.exclusionListLengthBefore).to.be.a('number');
    expect(recalculationRecord.exclusionListLengthAfter).to.be.a('number');

    aggregateStub.restore();
  });

  it('should handle other database errors normally (not BSON size)', async () => {
    for(let i=0; i<4; i++){
      await createMatchingUserGeneral(i);
    }

    const bsonSizeError = new mongoose.mongo.MongoServerError({
      message: "Network error",
      code: 105,
    });

    let aggregateStub = sinon.stub(User, 'aggregate').returns({
      read: sinon.stub().throws(bsonSizeError)
    });

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);

    expect(aggregateStub.calledOnce).to.be.false;

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(500);
    expect(res.text).to.equal('Something went wrong. Please try again.');

    expect(aggregateStub.calledOnce).to.be.true;

    const updatedUser = await User.findById('1');
    expect(updatedUser.exclusionListFailed).to.not.equal(true);
    expect(updatedUser.sizeOfExclusionUsedDuringFailure).to.be.undefined;

    const recalculationRecord = await ExclusionListRecalculation.findOne({ user: '1' });
    expect(recalculationRecord).to.be.null;

    aggregateStub.restore();
  });
});
