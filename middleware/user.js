const cmp = require('semver-compare');
const cryptoRandomString = require('crypto-random-string');
const { validate: validateEmail } = require('email-validator');
const haversine = require('haversine');
const moment = require('moment');
const http = require('../lib/http-errors');
const {
  notFoundError, forbiddenError, badRequestError, conflictError, applicationError, invalidInputError,
} = require('../lib/http-errors');
const User = require('../models/user');
const Translation = require('../models/translation');
const Action = require('../models/action');
const Block = require('../models/block');
const HideList = require('../models/hide-list');
const UserMetadata = require('../models/user-metadata');
const ExclusionList = require('../models/exclusion-list');
const FriendList = require('../models/friend-list');
const PurchaseReceipt = require('../models/purchase-receipt');
const BannedSource = require('../models/banned-source');
const WebVisitor = require('../models/web-visitor');
const AppVisitor = require('../models/app-visitor');
const basic = require('../lib/basic');
const admin = require('../config/firebase-admin');
const mailchimpLib = require('../lib/mailchimp');
const premiumLib = require('../lib/premium');
const chatLib = require('../lib/chat');
const userLib = require('../lib/user');
const reportLib = require('../lib/report');
const actionLib = require('../lib/action');
const { sendFlashSaleEmail, sendEmailTemplate, sendEmailTemplateSES, getCommonTranslatedTags } = require('../lib/email');
const handleLib = require('../lib/handle');
const { validatedNumber } = require('../lib/phone-numbers');
const { blockUser } = require('../lib/action');
const { locales } = require('../lib/translate');
const locationLib = require('../lib/location');
const constants = require('../lib/constants');
const geoip = require('geoip-lite');
const { i18n_interests, cleanInterestName } = require('../lib/interest');
const Interest = require('../models/interest');
const EmailSignup = require('../models/email-signup');
const RecaptchaLog = require('../models/recaptchaLog');
const { getBannedEmailDomains } = require('../lib/banned-email-domains');
const { appsflyerToKochava } = require('../lib/appsflyer')
const { bannedEmailDomains } = require('../lib/report-constants');
const interestLib = require('../lib/interest');
const HourlySystemMetric = require('../models/hourly-system-metrics');

const bannedErrorMessage = 'Your account has been restricted due to violations of our terms of service. If you believe this to be in error, please send us a <NAME_EMAIL>. Thank you for your understanding.';
const updateAppMessage = 'This version of the app is no longer supported. Please update to the latest version of Boo. Thank you!';

function getWebCategory(page) {
  if (page == '/database' || page.startsWith('/database/')) {
    return 'database';
  }
  if (page == '/u' || page.startsWith('/u/')) {
    return 'forums';
  }
  if (page == '/') {
    return 'home';
  }
  return page.split('/')[1] || 'other';
}

function isValidRelativeUrl(page) {
  if (typeof page !== 'string' || !page.startsWith('/')) {
    return false;
  }

  try {
    const url = new URL(page, 'https://boo.world');
  } catch (_) {
    return false;
  }

  return true;
}

function trimUrl(string) {
  const length = 2000;
  return string.length > length
    ? string.substring(0, length)
    : string;
}

async function processIp(user, ip, ua = '') {
  if (!user || !ip) {
    return;
  }

  if (user.ipData.ip == ip) {
    return;
  }

  const ipData = {
    date: Date.now(),
    ip: ip,
    userAgent: ua
  }

  const geo = geoip.lookup(ip);
  if (geo) {
    ipData.city = geo.city;
    ipData.region = geo.region;
    ipData.countryCode = geo.country;
    ipData.metro = geo.metro;
    ipData.area = geo.area;
    ipData.timezone = geo.timezone;

    if (geo.ll) {
      ipData.location = {
        type: 'Point',
        coordinates: [
          geo.ll[1],
          geo.ll[0],
        ],
      };
    }
    if (geo.country) {
      ipData.country = locationLib.getFullCountryName(geo.country);
    }
  }


  user.security.ipHistory.unshift(ipData);
  if (user.security.ipHistory.length > 10) {
    user.security.ipHistory.pop();
  }

  if (user.ipData.countryCode != ipData.countryCode) {
    user.security.internationalIpHistory.unshift(ipData);
    if (user.security.internationalIpHistory.length > 10) {
      user.security.internationalIpHistory.pop();
    }

    /*
    if (user.ipData.location && ipData.location) {
      // check if the location changed too quickly
      const start = {
        latitude: user.ipData.location.coordinates[1],
        longitude: user.ipData.location.coordinates[0],
      };
      const end = {
        latitude: ipData.location.coordinates[1],
        longitude: ipData.location.coordinates[0],
      };
      const distanceKm = haversine(start, end);
      const priorTime = user.ipData.date.getTime();
      const durationHr = (Date.now() - priorTime) / (3600 * 1000);
      const speedKmph = distanceKm / durationHr;
      if (distanceKm > 1000 && speedKmph > 1000) {
        await reportLib.createReport(
          user,
          null,
          ['Auto-report: IP location changed too quickly'],
          `${user.ipData.ip}, ${user.ipData.city}, ${user.ipData.region}, ${user.ipData.country} -> ${ipData.ip}, ${ipData.city}, ${ipData.region}, ${ipData.country}`,
          `Distance: ${distanceKm.toFixed(2)} km, duration: ${durationHr.toFixed(2)} hours, speed: ${speedKmph.toFixed(2)} km/h`,
        );
      }
    }
    */

  }

  if (['Nigeria', 'Ghana', 'Senegal', 'Togo'].includes(ipData.country)) {
    const bannedReason = ipData.country;
    await reportLib.autoShadowBan(user, bannedReason);
  }

  user.ipData = ipData;
}

module.exports = {

  updateAppMessage,

  async findUser(req, res, next) {
    // if user was already found, no need to find again
    if (req.user) {
      return next();
    }

    // find the user
    const user = await User.findByUid(req.uid);

    // if user found, save the user, uid, and locale
    if (user) {
      req.user = user;
      req.uid = user._id;
      const locale = user.locale && locales.includes(user.locale)
        ? user.locale
        : 'en';
      req.setLocale(locale);

      return next();
    }

    // if user creation route, proceed
    if (
      req.method == 'GET' && req.path == '/v1/user'
      || req.method == 'PUT' && req.path == '/v1/user/initApp'
    ) {
      return next();
    }

    // otherwise, not found error
    return next(http.notFoundError('User not found'));
  },

  verifyAdmin(req, res, next) {
    // assumes that findUser was previously called
    if (!req.user || !req.user.admin) {
      return next(forbiddenError());
    }
    return next();
  },

  async findBooSupportUser(req, res, next) {
    const user = await User.findById(chatLib.BOO_SUPPORT_ID);
    if (!user) {
      return next(http.applicationError());
    }
    req.booSupportUser = user;
    return next();
  },

  async findUserMetadata(req, res, next) {
    const userMetadata = await UserMetadata.findOne({ user: req.uid });
    if (!userMetadata) {
      return next(http.notFoundError('User not found'));
    }
    req.userMetadata = userMetadata;
    return next();
  },

  async findTargetUser(req, res, next) {
    if (req.query.user) {
      const user = await User.findById(req.query.user);
      if (!user) {
        return next(http.notFoundError('User not found'));
      }
      req.targetUser = user;
      return next();
    } else {
      return next();
    }
  },

  async findOtherUser(req, res, next) {
    const otherUserId = req.body.user;
    if (!otherUserId || typeof otherUserId !== 'string' || otherUserId.length > 4096) {
      return next(http.invalidInputError());
    }
    const otherUser = await User.findById(otherUserId);
    if (!otherUser) {
      return next(http.notFoundError('User not found'));
    }
    req.otherUser = otherUser;
    return next();
  },

  async verifyNotBlocked(req, res, next) {
    const otherUserId = req.body.user;
    if (!otherUserId || typeof otherUserId !== 'string' || otherUserId.length > 4096) {
      return next(http.invalidInputError());
    }

    const isBlocked = await Block.isEitherBlocked(req.uid, otherUserId);
    if (isBlocked) {
      return next(http.notFoundError());
    }

    return next();
  },

  validateEmailPhoneNumbers(req, res, next) {
    if (req.body.reset != undefined) {
      if (!typeof req.body.reset === 'boolean') return next(http.invalidInputError());
      if (req.body.reset === true) return next();
    }

    const start = new Date().getTime();

    const { emails } = req.body;
    let phoneNumbers = req.body.numbers;

    if (!Array.isArray(emails) || !Array.isArray(phoneNumbers)) return next(http.invalidInputError());

    if (!emails.every((email) => typeof email === 'string')) return next(http.invalidInputError());

    if (!phoneNumbers.every((phoneNumber) => typeof phoneNumber === 'string')) return next(http.invalidInputError());

    req.otherEmails = Array.from(new Set(emails.filter(validateEmail).map((email) => email.toLowerCase())));

    phoneNumbers = phoneNumbers.map(validatedNumber).filter((phoneNumber) => phoneNumber != null);
    req.otherNumbers = Array.from(new Set(phoneNumbers));

    const end = new Date().getTime();
    console.log(`User ${req.uid} Time to validate emails and numbers: ${end-start} ms. Emails length: ${emails.length}. Numbers length: ${phoneNumbers.length}`);

    return next();
  },

  deprecated(req, res, next) {
    return next(http.forbiddenError(req.__(updateAppMessage)));
  },

  checkVerified(req, res, next) {
    if (!constants.enforceVerification()) {
      return next();
    }
    const user = req.user;
    if (!user.isVerified()) {
      return next(http.forbiddenError(req.__('Verify your profile in order to use this feature.')));
    }
    return next();
  },

  checkSupportChatOrVerified(req, res, next) {
    const isSupportChat = req.uid !== chatLib.BOO_SUPPORT_ID && req.chat?.users?.some((x) => x._id === chatLib.BOO_SUPPORT_ID);
    if (isSupportChat) return next(http.forbiddenError(req.__(updateAppMessage)));

    if (!constants.enforceVerification()) return next();

    const user = req.user;
    if (user.isVerified()) return next();

    return next(http.forbiddenError(req.__('Verify your profile in order to use this feature.')));
  },

  updateSignInInfo(req, res, next) {
    if (!req.user) {
      return next();
    }

    // asynchronous
    admin.admin.auth().getUser(req.uid).then(async (userRecord) => {
      console.log(`Successfully fetched user data: ${JSON.stringify(userRecord)}`);

      if (!userRecord.emailVerified) console.log(`User ${req.uid}: Email is not verified`);

      const updateMail = (userRecord.emailVerified && (userRecord.email != req.user.email));
      const updateNumber = (userRecord.phoneNumber != req.user.phoneNumber);

      if (!(updateMail || updateNumber)) {
        console.log(`No Change in sign in info for user: ${req.uid}`);
        return;
      }
      console.log(`Initiating user sign in info update for user: ${req.uid}`);

      const updateQuery = {
        $set: {
        },
      };

      if (updateMail) {
        updateQuery.$set.email = userRecord.email;
        updateQuery.$set.emailDomain = basic.extractEmailDomain(userRecord.email);
      }

      if (updateNumber) updateQuery.$set.phoneNumber = userRecord.phoneNumber;

      const result = await User.updateOne(
        { _id: req.user._id },
        updateQuery,
      );
      const userData = await User.findOne({ _id: req.user._id });
      const user = userData;

      if (updateNumber) {
        console.log(`User ${req.uid}: changed phoneNumber from ${req.user.phoneNumber} to ${userData.phoneNumber}.`);
        if (
          !userData.banned && !userData.shadowBanned &&
          await BannedSource.isBanned({
            phoneNumber: userData.phoneNumber,
          })) {
          await reportLib.autoShadowBan(userData, 'loginSource already banned', userData.phoneNumber);
        }
      }

      if (updateMail) {
        console.log(`User ${req.uid}: changed email from ${req.user.email} to ${userData.email}.`);
        req.user.email = userData.email
        req.user.emailDomain = userData.emailDomain;
        if (
          userData.firstName
          && userData.metrics.numTipEmailsSent == 0
          && !(userData.pushNotificationSettings && (userData.pushNotificationSettings.email === false))
          && userData.locale == 'en'
          && !userData.events.finished_signup
        ) {
          const user = userData;
          sendEmailTemplateSES(
            user,
            'v2-welcome-email',
            '{{name}}, welcome to Boo',
            getCommonTranslatedTags(user),
          );
          /*
          const discount = premiumLib.getPremiumFlashSale(user);
          if (discount) {
            if (user.isConfigTrue('web_flash_sale_email_v3')) {
              await sendFlashSaleEmail(user);
            }
            user.metrics.numFlashSaleEmailsSent = 1;
          }
          */
          user.metrics.numTipEmailsSent = 1;
          await user.save();
          console.log(`Sent welcome email to user ${req.uid}  ${userRecord.email}`);
        }
        if (
          !userData.banned && !userData.shadowBanned &&
          await BannedSource.isBanned({
            email: userData.email,
          })) {
          await reportLib.autoShadowBan(userData, 'loginSource already banned', userData.email);
        }
      }

      HideList.userHiddenBy(req.user._id, updateMail ? userData.email : null, updateNumber ? userData.phoneNumber : null, null).then(
        (data) => Promise.all(data.map((blockById) => blockUser(blockById, req.user._id, res.io, true))),
        (err) => { `User ${req.uid}: Error updating hidden users after change mail: `, err; },
      );

      ///...
    }).catch((error) => {
      console.log(`User ${req.uid}: Error fetching user data:`, error);
    });

    // return immediately
    return next();
  },

  checkTeleportExpiration(req, res, next) {
    if (!req.user || !req.user.teleportLocation || premiumLib.isPremium(req.user) || req.user.isConfigTrue('make_teleport_free')) {
      return next();
    }

    userLib.resetTeleport(req.user);
    return next();
  },

  async checkIp(req, res, next) {
    const user = req.user;
    const ip = req.ip;

    if (!user || !ip) {
      return next();
    }

    if (user.ipData.ip == ip) {
      return next();
    }

    await processIp(user, ip);
    await user.save();

    return next();
  },

  async updateLastSeen(req, res, next) {
    if (!constants.updateLastSeenOnEveryRequest()) {
      return next();
    }

    const user = req.user;
    if (!user) {
      return next();
    }

    // update at most once per hour to prevent excessive updates
    if (user.metrics.lastSeen && moment().diff(user.metrics.lastSeen, 'hours') < 1) {
      return next();
    }

    user.metrics.lastSeen = new Date();
    await user.save();

    return next();
  },

  async checkCountryFilterExpiration(req, res, next) {
    const { user } = req;
    if (!user || !user.preferences.countries || premiumLib.isPremium(user)) {
      return next();
    }

    user.preferences.countries = undefined;
    await user.save();
    return next();
  },

  async checkSpritRealmExpiration(req, res, next) {
    const { user } = req;
    if (!user || !user.hideLocation || premiumLib.isPremium(user)) {
      return next();
    }

    user.hideLocation = undefined;
    user.calculateViewableInDailyProfiles();
    await user.save();
    return next();
  },

  async checkHideMyAgeExpiration(req, res, next) {
    const { user } = req;
    if (!user || !user.hideMyAge || premiumLib.isPremium(user)) {
      return next();
    }

    user.hideMyAge = undefined;
    await user.save();
    return next();
  },

  verifyNotBanned(req, res, next) {
    if (!req.user) {
      return next();
    }
    if (req.user.verified) {
      return next();
    }
    if (req.user.banned || req.user.disabled) {
      return next(http.forbiddenError(bannedErrorMessage));
    }

    return next();
  },

  verifyPictureLimit(req, res, next) {
    const pictures = req.user.originalPictures || req.user.pictures;
    if (pictures.length >= 9) {
      return next(http.invalidInputError('You have reached the maximum limit of 9 pictures'));
    }
    return next();
  },

  verifyPictureExists(req, res, next) {
    const pictures = req.user.originalPictures || req.user.pictures;
    const index = pictures.findIndex((id) => id == req.query.id);
    if (index < 0) {
      return next(http.notFoundError('The picture you are trying to replace could not be found'));
    }
    return next();
  },

  async createUserIfNotExist(req, res, next) {
    if (req.user) {
      return next();
    }

    const { appVersion, deviceId, partnerCampaign, os, appsflyer_id } = req.body;
    let { kochava, appsflyer } = req.body

    if (appsflyer) {
      if (appsflyer_id) {
          appsflyer.appsflyer_id = appsflyer_id;
      }
      const result = await appsflyerToKochava(appsflyer);
      if (result) {
          kochava = result; // Set kochava only if result is truthy
      }
    } else if (!appsflyer && appsflyer_id) {
        appsflyer = {
            appsflyer_id: appsflyer_id
        };
    }

    if (process.env.NO_RESTRICT_SIGNUP != '1') {
      if (!appVersion || cmp(appVersion, '1.12.0') <= 0) {
        return next(http.forbiddenError(req.__(updateAppMessage)));
      }
      const allowed = await userLib.isAuthAllowed(deviceId, req.email, req.ip, 'createUserIfNotExist');
      if (!allowed) {
        return next(http.forbiddenError("You've created too many accounts. Please login to one of your existing accounts."));
      }
    }

    console.log(`Creating user ${req.uid}`);
    const exclusionList = new ExclusionList({
      user: req.uid,
    });
    await exclusionList.save().catch((err) => {
      if (err.code != 11000) { // duplicate key error
        throw err;
      }
    });

    const userMetadata = new UserMetadata({
      user: req.uid,
    });
    await userMetadata.save().catch((err) => {
      if (err.code != 11000) { // duplicate key error
        throw err;
      }
    });

    const friendList = new FriendList({
      userId: req.uid,
    });
    await friendList.save().catch((err) => {
      if (err.code != 11000) { // duplicate key error
        throw err;
      }
    });

    const newUser = new User({
      _id: req.uid,
      handle: handleLib.getHandle(),
      relationshipTypePopupClosed: true,
      "metrics.currentExclusionListSize": 0,
      kochava,
      ...(appsflyer && { appsflyer })
    });
    let savedUser;
    try {
      savedUser = await newUser.save();
    } catch (err) {
      console.log(err);
    }
    if (!savedUser) {
      // the save failed, first check if the uid already exists
      savedUser = await User.findById(req.uid);
    }
    if (!savedUser) {
      // next try save with a handle that is guaranteed to be unique
      newUser.handle = Date.now().toString() + cryptoRandomString({ length: 10 });
      savedUser = await newUser.save(); // if this fails, we will give up and throw
      console.log(`User ${savedUser._id} fallback handle: ${savedUser.handle}`);
    }
    const user = savedUser;

    if (!kochava && partnerCampaign) {
      user.partnerCampaign = partnerCampaign;
    }

    // web source info
    if (isValidRelativeUrl(req.body.webSignupPage)) {
      const page = trimUrl(req.body.webSignupPage);
      user.webSignupPage = page;
      user.webSignupCategory = getWebCategory(page);
    }
    if (isValidRelativeUrl(req.body.webFirstVisitPage)) {
      const page = trimUrl(req.body.webFirstVisitPage);
      user.webFirstVisitPage = page;
      user.webFirstVisitCategory = getWebCategory(page);
    }
    if (req.body.webFirstVisitReferringURL) {
      try {
        const url = new URL(req.body.webFirstVisitReferringURL);
        const page = trimUrl(req.body.webFirstVisitReferringURL);
        user.webFirstVisitReferringURL = page;
        user.webFirstVisitReferringDomain = url.hostname;
      } catch (_) {}
    }
    if (req.body.webDeviceId) {
      user.deviceId = req.body.webDeviceId;
      user.webDeviceId = req.body.webDeviceId;
      let visitor = await WebVisitor.findOne({webDeviceId: req.body.webDeviceId});
      if (visitor) {
        visitor.events.signed_up = true;
        visitor.user = user._id;
        await visitor.save();
        for (const [key, value] of Object.entries(visitor.config)) {
          if (value != undefined) {
            user.config[key] = value;
          }
        }
      }
    }
    if (req.body.deviceId) {
      user.deviceId = req.body.deviceId;
      user.appDeviceId = req.body.deviceId;
      let visitor = await AppVisitor.findOne({deviceId: req.body.deviceId});
      if (visitor) {
        visitor.events.signed_up = true;
        visitor.user = user._id;
        await visitor.save();
        for (const [key, value] of Object.entries(visitor.config)) {
          if (value != undefined) {
            user.config[key] = value;
          }
        }
      }
    }

    // check user deviceId if already exist
    if(os?.toLowerCase() === 'ios' && deviceId){
      const query = {
        deviceId: deviceId,
        _id: { $ne: user._id },
        "metrics.madePurchase": true
      };
      const otherUser = await User.findOne(query, { _id: 1 });
      if(otherUser){
        user.alreadyPurchasedPremiumOnDeviceId = deviceId
        user.alreadyPurchasedPremiumOnUserId = otherUser._id
      }
    }


    const userAgent = req.get('User-Agent') || '';
    await processIp(user, req.ip, userAgent);
    await userLib.processDeviceInfo(req, user);
    userLib.processUtm(req, user);
    userLib.processLocale(req, user);
    user.initConfig();

    if (constants.requireManualVerificationForWeb()) {
      if (['1.12.16', '1.13.39', '1.13.46'].includes(appVersion)) {
        await reportLib.shadowBan(user, null, 'new signup on old version', appVersion);
      }
    }

    // immediate flash sale
    if (user.versionAtLeast('1.8.8') && !user.versionAtLeast('1.13.80')) {
      let hours = 6;
      user.activateFlashSale('first', hours);
    }

    await interestLib.autoAddInterestsForNicheOnboarding(user);    

    await user.save();

    await HourlySystemMetric.increment('numNewSignUps');

    HideList.userHiddenBy(user._id, null, null, user.deviceId).then(
      (data) => Promise.all(data.map((blockById) => blockUser(blockById, user._id, res.io, false, 'DeviceId', undefined, user.deviceId))),
      (err) => { `User ${req.uid}: Error updating hidden users based on DeviceId after update Device Info: `, err; },
    );

    if (req.phoneNumber) {
      const countryCode = req.phoneNumber.substring(0, 4);

      // Nigeria, Ghana, Senegal, or Togo - ban due to scammers
      let bannedReason = null;
      if (countryCode == '+234') {
        bannedReason = 'Nigeria';
      } else if (countryCode == '+233') {
        bannedReason = 'Ghana';
      } else if (countryCode == '+221') {
        bannedReason = 'Senegal';
      } else if (countryCode == '+228') {
        bannedReason = 'Togo';
      }

      if (bannedReason) {
        await reportLib.autoShadowBan(user, bannedReason);
      }
    }

    req.user = user;
    req.newSignup = true;

    // Check if email exists in email-signup collection and update signedUp to true
    const email = req.body.email || req.email;
    if (email) {
      await EmailSignup.updateOne(
        { email },
        { $set: { signedUp: true } },
      );

      await RecaptchaLog.updateMany(
         { email },
         { $set: { user: user._id } },
      );

      // ban users from known spam email domains
      const domain = basic.extractEmailDomain(email);
      if (bannedEmailDomains.includes(domain)) {
        await reportLib.shadowBan(user, null, 'banned email domain', domain);
      }
    }
    return next();
  },

  async validUserTranslation(req, res, next) {
    if (typeof req.query.id !== 'string') {
      return next(http.invalidInputError());
    }
    const translation = await Translation.findOne({
      _id: req.query.id,
      createdBy: req.user._id,
    });
    if (!translation) {
      return next(http.forbiddenError());
    }
    req.translation = translation;
    return next();
  },

};
