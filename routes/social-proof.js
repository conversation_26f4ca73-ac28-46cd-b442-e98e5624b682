const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
const socialProofLib = require('../lib/social-proof');
const socialLib = require('../lib/social');

module.exports = function () {
  router.get('/', asyncHandler(socialProofLib.getSocialProofRouteHandler));
  router.get('/super-like', asyncHandler(socialProofLib.getSuperLikeSocialProofRouteHandler));
  router.get('/coin-purchase', asyncHandler(socialProofLib.getCoinPurchaseSocialProofRouteHandler));
  router.get('/neuron-purchase', asyncHandler(socialProofLib.getNeuronPurchaseSocialProofRouteHandler));
  router.get('/boost-purchase', asyncHandler(socialProofLib.getBoostPurchaseSocialProofRouteHandler));
  router.get('/boost-success', asyncHandler(socialProofLib.getBoostPurchaseSuccessSocialProofRouteHandler));
  router.get('/verification', asyncHandler(socialProofLib.getSocialProofVerificationRouteHandler));
  router.get('/new-connections', asyncHandler(socialProofLib.getNewConnectionsRouteHandler));
  router.get('/new-signups', asyncHandler(socialProofLib.getNewSignupRouteHandler));

  router.get('/testimonials', asyncHandler(async (req, res, next) => {
    const testimonials = await socialLib.getTestimonials(req.user);
    res.json({
      testimonials,
    });
  }));

  return router;
};
